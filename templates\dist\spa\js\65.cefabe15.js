(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[65],{"54d6":function(a,t,n){"use strict";n.r(t);var e=function(){var a=this,t=a._self._c;return t("q-page",{staticClass:"flex flex-top"},[[t("div",{staticClass:"q-pa-md"},[t("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[t("q-tabs",{model:{value:a.detaillink,callback:function(t){a.detaillink=t},expression:"detaillink"}},[t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"outbounddashboard",label:a.$t("dashboards.outbound_statements"),icon:"img:statics/dashboard/out_statement.png",to:{name:"outbounddashboard"},exact:""}})],1),t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"inbounddashboard",label:a.$t("dashboards.inbound_statements"),icon:"img:statics/dashboard/in_statement.svg",to:{name:"inbounddashboard"},exact:""}})],1),t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"inboundAndOutbound",label:a.$t("dashboards.inbound_and_outbound_statements"),icon:"img:statics/dashboard/in_and_out_statement.svg",to:{name:"inboundAndOutbound"},exact:""}})],1)],1)],1)])],t("div",{staticClass:"main-table2"},[t("router-view")],1)],2)},s=[],o={name:"Pagedashboard",data(){return{detaillink:"outbounddashboard"}},methods:{}},d=o,i=n("42e1"),r=n("9989"),b=n("429b"),u=n("7867"),l=n("eebe"),c=n.n(l),m=Object(i["a"])(d,e,s,!1,null,null,null);t["default"]=m.exports;c()(m,"components",{QPage:r["a"],QTabs:b["a"],QRouteTab:u["a"]})}}]);