(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[76],{6238:function(t,a,e){"use strict";e.r(a);var n=function(){var t=this,a=t._self._c;return a("q-page",{staticClass:"flex flex-top"},[[a("div",{staticClass:"q-pa-md"},[a("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[a("q-tabs",{model:{value:t.detaillink,callback:function(a){t.detaillink=a},expression:"detaillink"}},[a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"stocklist",label:t.$t("stock.stocklist"),icon:"img:statics/stock/stocklist.png",to:{name:"stocklist"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"stockbinlist",label:t.$t("stock.stockbinlist"),icon:"img:statics/warehouse/binset.png",to:{name:"stockbinlist"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"emptybin",label:t.$t("stock.emptybin"),icon:"all_out",to:{name:"emptybin"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"occupiedbin",label:t.$t("stock.occupiedbin"),icon:"all_inbox",to:{name:"occupiedbin"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"cyclecount",label:t.$t("cyclecount"),icon:"img:statics/stock/cyclecount.png",to:{name:"cyclecount"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"cyclecountrecorder",label:t.$t("cyclecountrecorder"),icon:"img:statics/stock/cyclecountrecorder.png",to:{name:"cyclecountrecorder"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"handcount",label:t.$t("handcount.handcount"),icon:"img:statics/stock/cyclecount.png",to:{name:"handcount"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"handcountrecorder",label:t.$t("handcount.handcountrecorder"),icon:"img:statics/stock/cyclecountrecorder.png",to:{name:"handcountrecorder"},exact:""}})],1)],1)],1)])],a("div",{staticClass:"main-table"},[a("router-view")],1)],2)},c=[],o={name:"Pagestock",data(){return{detaillink:"stocklist"}},methods:{}},s=o,i=e("42e1"),r=e("9989"),l=e("429b"),m=e("7867"),u=e("eebe"),d=e.n(u),p=Object(i["a"])(s,n,c,!1,null,null,null);a["default"]=p.exports;d()(p,"components",{QPage:r["a"],QTabs:l["a"],QRouteTab:m["a"]})}}]);