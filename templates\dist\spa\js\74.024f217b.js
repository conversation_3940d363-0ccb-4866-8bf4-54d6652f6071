(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[74],{"2ed7":function(t,a,e){"use strict";e.r(a);var n=function(){var t=this,a=t._self._c;return a("q-page",{staticClass:"flex flex-top"},[[a("div",{staticClass:"q-pa-md"},[a("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[a("q-tabs",{model:{value:t.detaillink,callback:function(a){t.detaillink=a},expression:"detaillink"}},[a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"dn",label:t.$t("outbound.dn"),icon:"img:statics/outbound/dnlist.png",to:{name:"dn"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"freshorder",label:t.$t("outbound.freshorder"),icon:"img:statics/outbound/freshorder.png",to:{name:"freshorder"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"neworder",label:t.$t("outbound.neworder"),icon:"img:statics/outbound/order.png",to:{name:"neworder"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"backorder",label:t.$t("outbound.backorder"),icon:"img:statics/outbound/backorder.png",to:{name:"backorder"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"pickstock",label:t.$t("outbound.pickstock"),icon:"img:statics/outbound/pickstock.png",to:{name:"pickstock"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"pickedstock",label:t.$t("outbound.pickedstock"),icon:"img:statics/outbound/picked.png",to:{name:"pickedstock"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"pickinglist",label:t.$t("outbound.pickinglist"),icon:"img:statics/outbound/pickinglist.png",to:{name:"pickinglist"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"shippedstock",label:t.$t("outbound.shippedstock"),icon:"img:statics/outbound/outbound.png",to:{name:"shippedstock"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"pod",label:t.$t("outbound.pod"),icon:"img:statics/outbound/receiving.png",to:{name:"pod"},exact:""}})],1)],1)],1)])],a("div",{staticClass:"main-table"},[a("router-view")],1)],2)},o=[],i={name:"Pageoutbound",data(){return{detaillink:"dn"}},methods:{}},s=i,r=e("42e1"),c=e("9989"),d=e("429b"),u=e("7867"),l=e("eebe"),p=e.n(l),m=Object(r["a"])(s,n,o,!1,null,null,null);a["default"]=m.exports;p()(m,"components",{QPage:c["a"],QTabs:d["a"],QRouteTab:u["a"]})}}]);