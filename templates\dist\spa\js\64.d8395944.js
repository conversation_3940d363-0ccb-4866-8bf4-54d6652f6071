(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[64],{"5bf6":function(t,a,e){"use strict";e.r(a);var n=function(){var t=this,a=t._self._c;return a("q-page",{staticClass:"flex flex-top"},[[a("div",{staticClass:"q-pa-md"},[a("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[a("q-tabs",{model:{value:t.detaillink,callback:function(a){t.detaillink=a},expression:"detaillink"}},[a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"customerdnlist",label:t.$t("menuItem.customerdn"),icon:"img:statics/outbound/dnlist.png",to:{name:"customerdnlist"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"customerpod",label:t.$t("outbound.pod"),icon:"img:statics/outbound/receiving.png",to:{name:"customerpod"},exact:""}})],1)],1)],1)])],a("div",{staticClass:"main-table"},[a("router-view")],1)],2)},s=[],i={name:"Pagecustomerdn",data(){return{detaillink:"customerdnlist"}},methods:{}},o=i,l=e("42e1"),c=e("9989"),r=e("429b"),u=e("7867"),d=e("eebe"),m=e.n(d),p=Object(l["a"])(o,n,s,!1,null,null,null);a["default"]=p.exports;m()(p,"components",{QPage:c["a"],QTabs:r["a"],QRouteTab:u["a"]})}}]);