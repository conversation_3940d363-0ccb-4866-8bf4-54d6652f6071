# GreaterWMS 仓储管理系统功能完整性审查报告（修订版）

## 📋 项目概述

GreaterWMS是一个基于Django + Vue的开源仓储管理系统，支持多仓库管理、扫描PDA、移动APP等功能。该系统已针对化工行业进行了专门的功能增强，特别适合**丙类货物和塑料颗粒**等非危化品的精细化管理，具备较为完整的WMS核心功能。

**技术架构：**
- 后端：Django 4.1.2 + Django REST Framework
- 前端：Vue 2.6.0 + Quasar Framework
- 移动端：Cordova跨平台应用
- 数据库：SQLite(开发) / PostgreSQL(生产)

**业务特点：**
- 主要管理丙类货物（非危化品）
- 塑料颗粒等化工原料的精细化管理
- 全业务流程无纸化办公
- 多仓库数据隔离和权限管控

## 🎯 丙类货物与塑料颗粒管理特点分析

### 丙类货物特征
- **非危险品**：不涉及危化品管理复杂性
- **包装标准化**：通常为袋装、桶装、托盘装
- **批次管理重要**：需要严格的批次追溯
- **质量稳定性好**：相对简单的存储条件要求
- **流转频率高**：需要高效的进出库管理

### 塑料颗粒管理要点
- **颗粒规格管理**：粒径、颜色、牌号等规格参数
- **防潮要求**：需要控制湿度，防止受潮结块
- **批次纯度**：不同批次不能混合，影响产品质量
- **包装完整性**：防止包装破损导致颗粒散落
- **先进先出**：严格按生产日期执行FIFO原则

## 🎯 核心功能模块审查

### 1. 入库管理 (ASN - Advanced Shipping Notice)

**✅ 已实现功能：**
- ASN单据管理（AsnListModel, AsnDetailModel）
- 预收货通知处理
- 供应商信息管理
- 货物预分拣（AsnPreSortViewSet）
- 移库到库位（MoveToBinViewSet）
- 条码打印支持
- 文件导入导出

**⚠️ 针对丙类货物的不足：**
1. **塑料颗粒规格管理缺失**：缺少粒径、熔融指数、颜色等关键参数
2. **包装完整性检查不完善**：没有包装破损检查流程
3. **批次管理不完整**：ASN模型中没有批号字段
4. **湿度监控缺失**：塑料颗粒对湿度敏感，需要环境监控

**🔧 改进建议：**
```python
# 针对丙类货物和塑料颗粒的ASN增强
class AsnDetailModel(models.Model):
    # 现有字段...
    
    # 塑料颗粒专用字段
    batch_number = models.CharField(max_length=100, verbose_name="批次号", blank=True)
    production_date = models.DateField(verbose_name="生产日期", null=True, blank=True)
    expiry_date = models.DateField(verbose_name="过期日期", null=True, blank=True)
    
    # 塑料颗粒规格参数
    particle_size = models.CharField(max_length=50, verbose_name="粒径规格", blank=True)
    melt_index = models.DecimalField(max_digits=8, decimal_places=4, verbose_name="熔融指数", null=True, blank=True)
    color_code = models.CharField(max_length=50, verbose_name="颜色编码", blank=True)
    grade_specification = models.CharField(max_length=100, verbose_name="牌号规格", blank=True)
    
    # 包装检查
    package_condition = models.CharField(max_length=20, choices=[
        ('GOOD', '完好'),
        ('MINOR_DAMAGE', '轻微破损'),
        ('MAJOR_DAMAGE', '严重破损'),
        ('LEAKAGE', '泄漏'),
    ], default='GOOD', verbose_name="包装状态")
    
    # 环境监控
    humidity_on_arrival = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="到货湿度(%)", null=True, blank=True)
    temperature_on_arrival = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="到货温度(℃)", null=True, blank=True)
    
    # 质量状态
    quality_status = models.CharField(max_length=20, choices=[
        ('PENDING', '待检'),
        ('QUALIFIED', '合格'),
        ('UNQUALIFIED', '不合格'),
        ('QUARANTINE', '隔离'),
    ], default='PENDING', verbose_name="质检状态")
```

### 2. 出库管理 (DN - Delivery Note)

**✅ 已实现功能：**
- DN单据管理（DnListModel, DnDetailModel）
- 拣货清单管理（PickingListModel）
- 客户信息管理
- 缺货处理（back_order_label）

**⚠️ 针对塑料颗粒的不足：**
1. **批次FIFO控制不完善**：缺少自动按生产日期分配批次
2. **颗粒混批检查缺失**：不同批次颗粒不能混合发货
3. **包装规格匹配缺失**：客户要求的包装规格与库存不匹配时的处理
4. **质量证书管理缺失**：塑料颗粒需要随货提供质量证书

**🔧 改进建议：**
```python
# DN增强模型
class DnDetailEnhancedModel(models.Model):
    # 继承原有DN字段...
    
    # 批次分配
    allocated_batches = models.JSONField(default=list, verbose_name="分配批次")
    fifo_compliance = models.BooleanField(default=True, verbose_name="FIFO合规")
    batch_purity_check = models.BooleanField(default=True, verbose_name="批次纯度检查")
    
    # 包装要求
    required_package_type = models.CharField(max_length=50, verbose_name="要求包装类型", blank=True)
    required_package_weight = models.DecimalField(max_digits=8, decimal_places=3, verbose_name="要求包装重量", null=True, blank=True)
    
    # 质量文档
    quality_certificate_required = models.BooleanField(default=True, verbose_name="需要质量证书")
    quality_certificate_path = models.CharField(max_length=255, verbose_name="质量证书路径", blank=True)
    
    # 特殊要求
    moisture_content_limit = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="水分含量限制(%)", null=True, blank=True)
    special_handling_notes = models.TextField(verbose_name="特殊处理说明", blank=True)
```

### 3. 库存管理 (Stock) - 重点改进

**✅ 已实现功能：**
- 库存汇总管理（StockListModel）
- 库位库存管理（StockBinModel）
- 多种库存状态管理

**🚨 严重不足（影响塑料颗粒管理）：**
1. **批号管理完全缺失**：这是塑料颗粒管理的核心
2. **保质期管理缺失**：塑料颗粒有保质期要求
3. **湿度监控缺失**：影响颗粒质量的关键因素
4. **批次纯度控制缺失**：不同批次混合会影响产品质量

**🔧 重构建议：**
```python
# 重构库存模型以支持塑料颗粒管理
class PlasticGranuleStockModel(models.Model):
    """塑料颗粒库存模型"""
    
    # 基础信息
    goods_code = models.CharField(max_length=255, verbose_name="产品编码")
    goods_desc = models.CharField(max_length=255, verbose_name="产品描述")
    bin_name = models.CharField(max_length=255, verbose_name="库位")
    
    # 批次信息（核心）
    batch_number = models.CharField(max_length=100, verbose_name="批次号")
    production_date = models.DateField(verbose_name="生产日期")
    expiry_date = models.DateField(verbose_name="过期日期", null=True, blank=True)
    manufacturer = models.CharField(max_length=255, verbose_name="生产厂家")
    
    # 塑料颗粒规格
    grade_specification = models.CharField(max_length=100, verbose_name="牌号规格")
    particle_size = models.CharField(max_length=50, verbose_name="粒径", blank=True)
    melt_index = models.DecimalField(max_digits=8, decimal_places=4, verbose_name="熔融指数", null=True, blank=True)
    color_code = models.CharField(max_length=50, verbose_name="颜色编码", blank=True)
    density = models.DecimalField(max_digits=6, decimal_places=3, verbose_name="密度(g/cm³)", null=True, blank=True)
    
    # 数量管理
    total_quantity = models.DecimalField(max_digits=12, decimal_places=3, verbose_name="总数量")
    available_quantity = models.DecimalField(max_digits=12, decimal_places=3, verbose_name="可用数量")
    reserved_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0, verbose_name="预留数量")
    damaged_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0, verbose_name="损坏数量")
    
    # 质量状态
    quality_status = models.CharField(max_length=20, choices=[
        ('AVAILABLE', '可用'),
        ('QUARANTINE', '隔离'),
        ('HOLD', '保留'),
        ('DAMAGED', '损坏'),
        ('EXPIRED', '过期'),
    ], default='AVAILABLE', verbose_name="质量状态")
    
    # 环境监控
    current_humidity = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="当前湿度(%)", null=True, blank=True)
    current_temperature = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="当前温度(℃)", null=True, blank=True)
    last_environment_check = models.DateTimeField(verbose_name="最后环境检查时间", null=True, blank=True)
    
    # 包装信息
    package_type = models.CharField(max_length=50, verbose_name="包装类型")
    package_weight = models.DecimalField(max_digits=8, decimal_places=3, verbose_name="包装重量(kg)")
    package_count = models.IntegerField(verbose_name="包装件数")
    package_condition = models.CharField(max_length=20, default='GOOD', verbose_name="包装状态")
    
    # 成本信息
    unit_cost = models.DecimalField(max_digits=12, decimal_places=4, verbose_name="单位成本")
    total_value = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="总价值")
    
    # 移动历史
    last_movement_type = models.CharField(max_length=20, verbose_name="最后移动类型", blank=True)
    last_movement_date = models.DateTimeField(verbose_name="最后移动时间", null=True, blank=True)
    
    # 预警设置
    low_stock_threshold = models.DecimalField(max_digits=12, decimal_places=3, verbose_name="低库存阈值", default=0)
    expiry_alert_days = models.IntegerField(default=30, verbose_name="过期预警天数")
    
    # 通用字段
    openid = models.CharField(max_length=255, verbose_name="租户ID")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'plastic_granule_stock'
        verbose_name = '塑料颗粒库存'
        unique_together = ['goods_code', 'batch_number', 'bin_name', 'openid']
        indexes = [
            models.Index(fields=['goods_code', 'batch_number']),
            models.Index(fields=['expiry_date']),
            models.Index(fields=['quality_status']),
            models.Index(fields=['production_date']),
        ]

    @property
    def days_to_expiry(self):
        """距离过期天数"""
        if self.expiry_date:
            from django.utils import timezone
            delta = self.expiry_date - timezone.now().date()
            return delta.days
        return None

    @property
    def is_near_expiry(self):
        """是否临近过期"""
        days = self.days_to_expiry
        return days is not None and days <= self.expiry_alert_days

    @property
    def utilization_rate(self):
        """库存利用率"""
        if self.total_quantity > 0:
            return (self.available_quantity / self.total_quantity) * 100
        return 0
```

### 4. 库位管理 (BinSet) - 针对塑料颗粒优化

**✅ 已实现功能：**
- 库位基础信息管理
- 库位尺寸和属性管理
- 空库位标识

**⚠️ 塑料颗粒存储不足：**
1. **湿度控制区域划分缺失**：塑料颗粒需要低湿度环境
2. **重量容量限制缺失**：颗粒密度高，需要重量限制
3. **通风要求管理缺失**：防止颗粒受潮需要通风
4. **批次隔离要求缺失**：不同批次需要物理隔离

**🔧 改进建议：**
```python
class PlasticGranuleBinModel(models.Model):
    """塑料颗粒专用库位模型"""
    
    # 继承基础库位信息
    bin_name = models.CharField(max_length=255, unique=True, verbose_name="库位编码")
    bin_zone = models.CharField(max_length=50, verbose_name="库位区域")
    
    # 物理属性
    length = models.DecimalField(max_digits=8, decimal_places=3, verbose_name="长度(m)")
    width = models.DecimalField(max_digits=8, decimal_places=3, verbose_name="宽度(m)")
    height = models.DecimalField(max_digits=8, decimal_places=3, verbose_name="高度(m)")
    volume_capacity = models.DecimalField(max_digits=10, decimal_places=3, verbose_name="体积容量(m³)")
    weight_capacity = models.DecimalField(max_digits=10, decimal_places=3, verbose_name="重量容量(kg)")
    
    # 环境控制
    humidity_controlled = models.BooleanField(default=False, verbose_name="湿度控制")
    target_humidity = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="目标湿度(%)", null=True, blank=True)
    ventilation_required = models.BooleanField(default=False, verbose_name="通风要求")
    temperature_controlled = models.BooleanField(default=False, verbose_name="温度控制")
    
    # 存储限制
    single_batch_only = models.BooleanField(default=True, verbose_name="单批次存储")
    max_batch_age_days = models.IntegerField(verbose_name="最大批次存储天数", null=True, blank=True)
    compatible_materials = models.JSONField(default=list, verbose_name="兼容材料类型")
    
    # 当前状态
    current_weight = models.DecimalField(max_digits=10, decimal_places=3, default=0, verbose_name="当前重量")
    current_volume = models.DecimalField(max_digits=10, decimal_places=3, default=0, verbose_name="当前体积")
    current_batch = models.CharField(max_length=100, verbose_name="当前批次", blank=True)
    current_material = models.CharField(max_length=255, verbose_name="当前物料", blank=True)
    
    # 库位状态
    bin_status = models.CharField(max_length=20, choices=[
        ('AVAILABLE', '可用'),
        ('OCCUPIED', '占用'),
        ('RESERVED', '预留'),
        ('MAINTENANCE', '维护中'),
        ('BLOCKED', '锁定'),
    ], default='AVAILABLE', verbose_name="库位状态")
    
    # 设备信息
    humidity_sensor_id = models.CharField(max_length=50, verbose_name="湿度传感器ID", blank=True)
    temperature_sensor_id = models.CharField(max_length=50, verbose_name="温度传感器ID", blank=True)
    ventilation_system_id = models.CharField(max_length=50, verbose_name="通风系统ID", blank=True)
    
    @property
    def weight_utilization(self):
        """重量利用率"""
        if self.weight_capacity > 0:
            return (self.current_weight / self.weight_capacity) * 100
        return 0

    @property
    def volume_utilization(self):
        """体积利用率"""
        if self.volume_capacity > 0:
            return (self.current_volume / self.volume_capacity) * 100
        return 0

    @property
    def is_batch_pure(self):
        """批次纯度检查"""
        if self.single_batch_only and self.current_batch:
            # 检查库位中是否只有一个批次
            from .models import PlasticGranuleStockModel
            batches = PlasticGranuleStockModel.objects.filter(
                bin_name=self.bin_name
            ).values_list('batch_number', flat=True).distinct()
            return len(batches) <= 1
        return True
```

## 📱 全业务无纸化办公设计方案

### 🎯 无纸化办公核心目标

1. **消除纸质单据**：所有业务单据电子化
2. **移动端操作**：现场作业全部通过移动设备完成
3. **电子签名确认**：关键节点电子签名替代手工签字
4. **实时数据同步**：所有操作实时更新到系统
5. **电子档案管理**：文档电子化存储和检索

### 📋 无纸化业务流程设计

#### 1. 入库无纸化流程

```mermaid
graph TD
    A[供应商发货通知] --> B[电子ASN创建]
    B --> C[司机移动端确认到货]
    C --> D[收货员PDA扫码验收]
    D --> E[移动端拍照记录]
    E --> F[电子签名确认]
    F --> G[自动生成入库单]
    G --> H[质检员移动端质检]
    H --> I[电子质检报告]
    I --> J[自动上架指令]
    J --> K[PDA扫码上架确认]
```

**技术实现：**
```python
class PaperlessReceiptWorkflow(models.Model):
    """无纸化收货工作流"""
    
    workflow_id = models.CharField(max_length=50, unique=True, verbose_name="工作流ID")
    asn_code = models.CharField(max_length=255, verbose_name="ASN编号")
    
    # 工作流步骤
    steps = models.JSONField(default=list, verbose_name="工作流步骤")
    current_step = models.IntegerField(default=0, verbose_name="当前步骤")
    
    # 电子签名记录
    signatures = models.JSONField(default=list, verbose_name="电子签名记录")
    
    # 照片记录
    photos = models.JSONField(default=list, verbose_name="照片记录")
    
    # 扫码记录
    scan_records = models.JSONField(default=list, verbose_name="扫码记录")
    
    # 移动端操作记录
    mobile_operations = models.JSONField(default=list, verbose_name="移动端操作")
    
    # 状态管理
    status = models.CharField(max_length=20, choices=[
        ('STARTED', '已开始'),
        ('IN_PROGRESS', '进行中'),
        ('COMPLETED', '已完成'),
        ('EXCEPTION', '异常'),
    ], default='STARTED', verbose_name="状态")
    
    def add_step_completion(self, step_name, operator, signature_data=None, photos=None, scan_data=None):
        """添加步骤完成记录"""
        completion_record = {
            'step_name': step_name,
            'operator': operator,
            'timestamp': timezone.now().isoformat(),
            'signature': signature_data,
            'photos': photos or [],
            'scan_data': scan_data,
        }
        
        if not self.steps:
            self.steps = []
        self.steps.append(completion_record)
        self.current_step += 1
        self.save()
```

#### 2. 出库无纸化流程

```mermaid
graph TD
    A[客户订单] --> B[电子DN生成]
    B --> C[系统自动分配批次]
    C --> D[生成拣货任务]
    D --> E[拣货员PDA接收任务]
    E --> F[扫码拣货确认]
    F --> G[移动端拍照记录]
    G --> H[质检员移动端复核]
    H --> I[包装员扫码包装]
    I --> J[司机电子签名提货]
    J --> K[自动生成出库单]
```

#### 3. 盘点无纸化流程

```mermaid
graph TD
    A[系统生成盘点计划] --> B[移动端推送盘点任务]
    B --> C[盘点员PDA扫码盘点]
    C --> D[实时录入盘点数据]
    D --> E[移动端拍照异常]
    E --> F[主管移动端审核]
    F --> G[电子签名确认]
    G --> H[自动生成盘点报告]
```

### 📱 移动端功能模块设计

#### 1. 移动端核心功能架构

```javascript
// 移动端应用架构
src/
├── pages/
│   ├── Receipt/              // 收货模块
│   │   ├── ReceiptScan.vue   // 扫码收货
│   │   ├── QualityCheck.vue  // 质量检查
│   │   ├── PhotoCapture.vue  // 拍照记录
│   │   └── SignatureCapture.vue // 电子签名
│   ├── Shipping/             // 发货模块
│   │   ├── PickingTask.vue   // 拣货任务
│   │   ├── PackingConfirm.vue // 包装确认
│   │   └── LoadingConfirm.vue // 装车确认
│   ├── Inventory/            // 库存模块
│   │   ├── CycleCount.vue    // 循环盘点
│   │   ├── StockTransfer.vue // 库存移动
│   │   └── StockInquiry.vue  // 库存查询
│   └── Common/               // 通用模块
│       ├── Scanner.vue       // 扫码组件
│       ├── Camera.vue        // 拍照组件
│       ├── Signature.vue     // 签名组件
│       └── OfflineSync.vue   // 离线同步
```

#### 2. 关键移动端组件实现

**扫码组件：**
```vue
<template>
  <div class="scanner-container">
    <qr-scanner
      @decode="onDecode"
      @init="onInit"
      :track="trackFunctionSelected"
    />
    <div class="scan-result">
      <q-input
        v-model="scanResult"
        label="扫码结果"
        readonly
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'UniversalScanner',
  data() {
    return {
      scanResult: '',
      scanHistory: []
    }
  },
  methods: {
    onDecode(result) {
      this.scanResult = result
      this.processScanResult(result)
    },
    
    processScanResult(result) {
      // 解析不同类型的条码
      const barcodeType = this.identifyBarcodeType(result)
      
      switch(barcodeType) {
        case 'GOODS_CODE':
          this.handleGoodsCodeScan(result)
          break
        case 'BATCH_NUMBER':
          this.handleBatchNumberScan(result)
          break
        case 'BIN_LOCATION':
          this.handleBinLocationScan(result)
          break
        case 'ASN_CODE':
          this.handleAsnCodeScan(result)
          break
        default:
          this.handleUnknownScan(result)
      }
    },
    
    identifyBarcodeType(barcode) {
      // 根据条码格式识别类型
      if (barcode.startsWith('GC-')) return 'GOODS_CODE'
      if (barcode.startsWith('BN-')) return 'BATCH_NUMBER'
      if (barcode.startsWith('BL-')) return 'BIN_LOCATION'
      if (barcode.startsWith('ASN-')) return 'ASN_CODE'
      return 'UNKNOWN'
    }
  }
}
</script>
```

**电子签名组件：**
```vue
<template>
  <div class="signature-container">
    <canvas
      ref="signatureCanvas"
      @touchstart="startDrawing"
      @touchmove="draw"
      @touchend="stopDrawing"
      class="signature-canvas"
    />
    <div class="signature-actions">
      <q-btn @click="clearSignature" label="清除" />
      <q-btn @click="saveSignature" label="保存" color="primary" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElectronicSignature',
  data() {
    return {
      isDrawing: false,
      canvas: null,
      ctx: null
    }
  },
  mounted() {
    this.initCanvas()
  },
  methods: {
    initCanvas() {
      this.canvas = this.$refs.signatureCanvas
      this.ctx = this.canvas.getContext('2d')
      this.ctx.strokeStyle = '#000000'
      this.ctx.lineWidth = 2
      this.ctx.lineCap = 'round'
    },
    
    startDrawing(event) {
      this.isDrawing = true
      const rect = this.canvas.getBoundingClientRect()
      const x = event.touches[0].clientX - rect.left
      const y = event.touches[0].clientY - rect.top
      this.ctx.beginPath()
      this.ctx.moveTo(x, y)
    },
    
    draw(event) {
      if (!this.isDrawing) return
      const rect = this.canvas.getBoundingClientRect()
      const x = event.touches[0].clientX - rect.left
      const y = event.touches[0].clientY - rect.top
      this.ctx.lineTo(x, y)
      this.ctx.stroke()
    },
    
    stopDrawing() {
      this.isDrawing = false
    },
    
    saveSignature() {
      const signatureData = this.canvas.toDataURL()
      this.$emit('signature-saved', {
        data: signatureData,
        timestamp: new Date().toISOString(),
        operator: this.$store.state.user.name
      })
    },
    
    clearSignature() {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
    }
  }
}
</script>
```

### 📊 电子单据系统设计

#### 1. 电子单据模板引擎

```python
class ElectronicDocumentTemplate(models.Model):
    """电子单据模板"""
    
    template_id = models.CharField(max_length=50, unique=True, verbose_name="模板ID")
    template_name = models.CharField(max_length=100, verbose_name="模板名称")
    document_type = models.CharField(max_length=20, choices=[
        ('ASN', '入库单'),
        ('DN', '出库单'),
        ('TRANSFER', '移库单'),
        ('COUNT', '盘点单'),
        ('QUALITY', '质检单'),
    ], verbose_name="单据类型")
    
    # 模板内容
    template_content = models.JSONField(verbose_name="模板内容")
    header_fields = models.JSONField(default=list, verbose_name="表头字段")
    detail_fields = models.JSONField(default=list, verbose_name="明细字段")
    signature_fields = models.JSONField(default=list, verbose_name="签名字段")
    
    # 打印设置
    page_size = models.CharField(max_length=10, default='A4', verbose_name="页面尺寸")
    orientation = models.CharField(max_length=10, choices=[
        ('portrait', '纵向'),
        ('landscape', '横向'),
    ], default='portrait', verbose_name="页面方向")
    
    # 状态
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    is_default = models.BooleanField(default=False, verbose_name="是否默认")
    
    # 通用字段
    openid = models.CharField(max_length=255, verbose_name="租户ID")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

class ElectronicDocument(models.Model):
    """电子单据实例"""
    
    document_id = models.CharField(max_length=50, unique=True, verbose_name="单据ID")
    template = models.ForeignKey(ElectronicDocumentTemplate, on_delete=models.CASCADE, verbose_name="使用模板")
    
    # 单据数据
    document_data = models.JSONField(verbose_name="单据数据")
    generated_content = models.TextField(verbose_name="生成内容")
    
    # 签名记录
    signatures = models.JSONField(default=list, verbose_name="签名记录")
    
    # 状态管理
    status = models.CharField(max_length=20, choices=[
        ('DRAFT', '草稿'),
        ('GENERATED', '已生成'),
        ('SIGNED', '已签名'),
        ('PRINTED', '已打印'),
        ('ARCHIVED', '已归档'),
    ], default='DRAFT', verbose_name="状态")
    
    # 生成信息
    generated_by = models.CharField(max_length=100, verbose_name="生成人")
    generated_time = models.DateTimeField(auto_now_add=True, verbose_name="生成时间")
    
    # 通用字段
    openid = models.CharField(max_length=255, verbose_name="租户ID")
```

#### 2. 无纸化工作流引擎

```python
class PaperlessWorkflowEngine:
    """无纸化工作流引擎"""
    
    def __init__(self):
        self.workflow_definitions = {
            'RECEIPT': self.get_receipt_workflow(),
            'SHIPPING': self.get_shipping_workflow(),
            'TRANSFER': self.get_transfer_workflow(),
            'COUNT': self.get_count_workflow(),
        }
    
    def get_receipt_workflow(self):
        """收货工作流定义"""
        return [
            {
                'step_id': 'arrival_confirm',
                'step_name': '到货确认',
                'required_actions': ['scan_asn', 'photo_truck'],
                'required_signature': True,
                'mobile_only': True,
            },
            {
                'step_id': 'goods_inspection',
                'step_name': '货物检查',
                'required_actions': ['scan_goods', 'check_package', 'record_environment'],
                'required_signature': True,
                'mobile_only': True,
            },
            {
                'step_id': 'quality_check',
                'step_name': '质量检验',
                'required_actions': ['sample_test', 'record_result'],
                'required_signature': True,
                'mobile_only': False,
            },
            {
                'step_id': 'putaway',
                'step_name': '上架确认',
                'required_actions': ['scan_bin', 'confirm_quantity'],
                'required_signature': True,
                'mobile_only': True,
            }
        ]
    
    def get_shipping_workflow(self):
        """发货工作流定义"""
        return [
            {
                'step_id': 'pick_planning',
                'step_name': '拣货计划',
                'required_actions': ['generate_pick_list', 'allocate_batch'],
                'required_signature': False,
                'mobile_only': False,
            },
            {
                'step_id': 'picking',
                'step_name': '拣货作业',
                'required_actions': ['scan_pick_location', 'scan_goods', 'confirm_quantity'],
                'required_signature': True,
                'mobile_only': True,
            },
            {
                'step_id': 'packing',
                'step_name': '包装作业',
                'required_actions': ['scan_package', 'weight_check', 'label_print'],
                'required_signature': True,
                'mobile_only': True,
            },
            {
                'step_id': 'loading',
                'step_name': '装车确认',
                'required_actions': ['scan_truck', 'photo_loading'],
                'required_signature': True,
                'mobile_only': True,
            }
        ]
    
    def start_workflow(self, workflow_type, reference_id, operator):
        """启动工作流"""
        workflow_instance = PaperlessWorkflowInstance.objects.create(
            workflow_type=workflow_type,
            reference_id=reference_id,
            workflow_definition=self.workflow_definitions[workflow_type],
            current_step=0,
            status='STARTED',
            started_by=operator
        )
        return workflow_instance
    
    def complete_step(self, workflow_instance, step_data):
        """完成工作流步骤"""
        current_step = workflow_instance.workflow_definition[workflow_instance.current_step]
        
        # 验证必需操作
        for required_action in current_step['required_actions']:
            if required_action not in step_data.get('actions', {}):
                raise ValueError(f"缺少必需操作: {required_action}")
        
        # 验证签名
        if current_step['required_signature'] and not step_data.get('signature'):
            raise ValueError("此步骤需要电子签名")
        
        # 记录步骤完成
        workflow_instance.add_step_completion(
            step_name=current_step['step_name'],
            operator=step_data['operator'],
            signature_data=step_data.get('signature'),
            photos=step_data.get('photos'),
            scan_data=step_data.get('scan_data')
        )
        
        # 检查是否完成所有步骤
        if workflow_instance.current_step >= len(workflow_instance.workflow_definition):
            workflow_instance.status = 'COMPLETED'
            workflow_instance.completed_time = timezone.now()
            workflow_instance.save()
        
        return workflow_instance

class PaperlessWorkflowInstance(models.Model):
    """无纸化工作流实例"""
    
    workflow_id = models.CharField(max_length=50, unique=True, verbose_name="工作流ID")
    workflow_type = models.CharField(max_length=20, verbose_name="工作流类型")
    reference_id = models.CharField(max_length=100, verbose_name="关联单据ID")
    
    # 工作流定义和状态
    workflow_definition = models.JSONField(verbose_name="工作流定义")
    current_step = models.IntegerField(default=0, verbose_name="当前步骤")
    completed_steps = models.JSONField(default=list, verbose_name="已完成步骤")
    
    # 状态管理
    status = models.CharField(max_length=20, choices=[
        ('STARTED', '已开始'),
        ('IN_PROGRESS', '进行中'),
        ('COMPLETED', '已完成'),
        ('SUSPENDED', '已暂停'),
        ('CANCELLED', '已取消'),
    ], default='STARTED', verbose_name="状态")
    
    # 时间记录
    started_by = models.CharField(max_length=100, verbose_name="启动人")
    started_time = models.DateTimeField(auto_now_add=True, verbose_name="启动时间")
    completed_time = models.DateTimeField(null=True, blank=True, verbose_name="完成时间")
    
    # 通用字段
    openid = models.CharField(max_length=255, verbose_name="租户ID")
    
    def add_step_completion(self, step_name, operator, signature_data=None, photos=None, scan_data=None):
        """添加步骤完成记录"""
        completion_record = {
            'step_name': step_name,
            'operator': operator,
            'timestamp': timezone.now().isoformat(),
            'signature': signature_data,
            'photos': photos or [],
            'scan_data': scan_data,
        }
        
        if not self.completed_steps:
            self.completed_steps = []
        self.completed_steps.append(completion_record)
        self.current_step += 1
        self.save()
```

## 🏭 多仓库管理与第三方仓储审查

### 当前多仓库实现状态

**✅ 已实现：**
- 仓库基础信息管理（warehouse/models.py）
- 数据隔离机制（openid字段）
- 多租户架构支持

**⚠️ 第三方仓储功能不足：**
1. **货主权限管理缺失**：货主无法直接查看自己的库存
2. **客户门户缺失**：没有专门的客户查询界面
3. **数据权限隔离不够精细**：缺少按客户的数据隔离
4. **计费管理功能薄弱**：缺少自动计费和账单生成

### 🔧 第三方仓储完整解决方案

#### 1. 客户权限管理系统

```python
class CustomerPermissionModel(models.Model):
    """客户权限管理"""
    
    customer_code = models.CharField(max_length=100, verbose_name="客户编码")
    customer_name = models.CharField(max_length=255, verbose_name="客户名称")
    
    # 登录凭证
    login_username = models.CharField(max_length=100, unique=True, verbose_name="登录用户名")
    login_password = models.CharField(max_length=255, verbose_name="登录密码")
    
    # 权限设置
    can_view_inventory = models.BooleanField(default=True, verbose_name="可查看库存")
    can_view_transactions = models.BooleanField(default=True, verbose_name="可查看交易记录")
    can_download_reports = models.BooleanField(default=True, verbose_name="可下载报表")
    can_create_orders = models.BooleanField(default=False, verbose_name="可创建订单")
    
    # 数据范围限制
    accessible_warehouses = models.JSONField(default=list, verbose_name="可访问仓库")
    accessible_goods = models.JSONField(default=list, verbose_name="可访问货物")
    
    # 状态管理
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    last_login_time = models.DateTimeField(null=True, blank=True, verbose_name="最后登录时间")
    
    # 通用字段
    openid = models.CharField(max_length=255, verbose_name="租户ID")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

class CustomerInventoryView(models.Model):
    """客户库存视图"""
    
    customer_code = models.CharField(max_length=100, verbose_name="客户编码")
    warehouse_code = models.CharField(max_length=100, verbose_name="仓库编码")
    goods_code = models.CharField(max_length=255, verbose_name="产品编码")
    goods_desc = models.CharField(max_length=255, verbose_name="产品描述")
    batch_number = models.CharField(max_length=100, verbose_name="批次号")
    
    # 数量信息
    total_quantity = models.DecimalField(max_digits=12, decimal_places=3, verbose_name="总数量")
    available_quantity = models.DecimalField(max_digits=12, decimal_places=3, verbose_name="可用数量")
    reserved_quantity = models.DecimalField(max_digits=12, decimal_places=3, verbose_name="预留数量")
    
    # 位置信息
    bin_locations = models.JSONField(default=list, verbose_name="库位列表")
    
    # 质量状态
    quality_status = models.CharField(max_length=20, verbose_name="质量状态")
    
    # 时间信息
    production_date = models.DateField(verbose_name="生产日期", null=True, blank=True)
    expiry_date = models.DateField(verbose_name="过期日期", null=True, blank=True)
    last_movement_date = models.DateTimeField(verbose_name="最后移动时间", null=True, blank=True)
    
    # 成本信息（客户可见）
    unit_value = models.DecimalField(max_digits=12, decimal_places=4, verbose_name="单位价值")
    total_value = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="总价值")
    
    class Meta:
        db_table = 'customer_inventory_view'
        verbose_name = '客户库存视图'
        unique_together = ['customer_code', 'goods_code', 'batch_number', 'warehouse_code']
```

#### 2. 客户门户API设计

```python
class CustomerPortalViewSet(viewsets.ReadOnlyModelViewSet):
    """客户门户API"""
    
    permission_classes = [IsCustomerAuthenticated]
    
    def get_queryset(self):
        """获取客户可访问的数据"""
        customer_code = self.request.user.customer_code
        return CustomerInventoryView.objects.filter(customer_code=customer_code)
    
    @action(detail=False, methods=['get'])
    def inventory_summary(self, request):
        """库存汇总"""
        customer_code = request.user.customer_code
        
        summary = CustomerInventoryView.objects.filter(
            customer_code=customer_code
        ).aggregate(
            total_skus=Count('goods_code', distinct=True),
            total_batches=Count('batch_number', distinct=True),
            total_quantity=Sum('total_quantity'),
            total_value=Sum('total_value')
        )
        
        return Response(summary)
    
    @action(detail=False, methods=['get'])
    def expiry_alerts(self, request):
        """过期预警"""
        customer_code = request.user.customer_code
        alert_days = int(request.query_params.get('days', 30))
        
        near_expiry = CustomerInventoryView.objects.filter(
            customer_code=customer_code,
            expiry_date__lte=timezone.now().date() + timedelta(days=alert_days),
            expiry_date__gt=timezone.now().date()
        ).order_by('expiry_date')
        
        serializer = CustomerInventorySerializer(near_expiry, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def transaction_history(self, request):
        """交易历史"""
        customer_code = request.user.customer_code
        
        transactions = CustomerTransactionLog.objects.filter(
            customer_code=customer_code
        ).order_by('-operation_time')
        
        # 分页处理
        page = self.paginate_queryset(transactions)
        if page is not None:
            serializer = CustomerTransactionSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = CustomerTransactionSerializer(transactions, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def download_report(self, request):
        """下载报表"""
        customer_code = request.user.customer_code
        report_type = request.data.get('report_type')
        date_from = request.data.get('date_from')
        date_to = request.data.get('date_to')
        
        # 生成报表
        report_generator = CustomerReportGenerator(customer_code)
        report_file = report_generator.generate_report(
            report_type, date_from, date_to
        )
        
        return Response({
            'download_url': report_file.url,
            'file_name': report_file.name,
            'generated_time': timezone.now().isoformat()
        })
```

#### 3. 客户门户前端设计

```vue
<!-- 客户门户主界面 -->
<template>
  <q-layout view="lHh Lpr lFf">
    <!-- 头部导航 -->
    <q-header elevated>
      <q-toolbar>
        <q-toolbar-title>
          {{ customerInfo.name }} - 库存管理门户
        </q-toolbar-title>
        <q-btn flat @click="logout">退出</q-btn>
      </q-toolbar>
    </q-header>
    
    <!-- 侧边导航 -->
    <q-drawer v-model="leftDrawerOpen" show-if-above bordered>
      <q-list>
        <q-item-label header>功能菜单</q-item-label>
        
        <q-item clickable @click="$router.push('/inventory')">
          <q-item-section avatar>
            <q-icon name="inventory" />
          </q-item-section>
          <q-item-section>库存查询</q-item-section>
        </q-item>
        
        <q-item clickable @click="$router.push('/transactions')">
          <q-item-section avatar>
            <q-icon name="receipt" />
          </q-item-section>
          <q-item-section>交易记录</q-item-section>
        </q-item>
        
        <q-item clickable @click="$router.push('/reports')">
          <q-item-section avatar>
            <q-icon name="assessment" />
          </q-item-section>
          <q-item-section>报表下载</q-item-section>
        </q-item>
        
        <q-item clickable @click="$router.push('/alerts')">
          <q-item-section avatar>
            <q-icon name="warning" />
          </q-item-section>
          <q-item-section>
            过期预警
            <q-badge v-if="alertCount > 0" color="red" floating>
              {{ alertCount }}
            </q-badge>
          </q-item-section>
        </q-item>
      </q-list>
    </q-drawer>
    
    <!-- 主内容区域 -->
    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script>
export default {
  name: 'CustomerPortalLayout',
  data() {
    return {
      leftDrawerOpen: false,
      customerInfo: {},
      alertCount: 0
    }
  },
  async mounted() {
    await this.loadCustomerInfo()
    await this.loadAlertCount()
  },
  methods: {
    async loadCustomerInfo() {
      try {
        const response = await this.$api.get('/customer/profile')
        this.customerInfo = response.data
      } catch (error) {
        this.$q.notify({
          type: 'negative',
          message: '加载客户信息失败'
        })
      }
    },
    
    async loadAlertCount() {
      try {
        const response = await this.$api.get('/customer/expiry-alerts')
        this.alertCount = response.data.length
      } catch (error) {
        console.error('加载预警数量失败:', error)
      }
    },
    
    logout() {
      this.$store.dispatch('auth/logout')
      this.$router.push('/login')
    }
  }
}
</script>
```

## 📊 与市场同类产品对比分析

### 主流WMS产品功能对比（针对丙类货物管理）

| 功能模块 | GreaterWMS | 金蝶云星空 | 用友U8+ | SAP WM | 评价与建议 |
|---------|------------|-----------|---------|---------|-----------|
| **批号管理** | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 🚨 最大短板，需重点改进 |
| **塑料颗粒专业功能** | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | 需要行业专业化增强 |
| **无纸化办公** | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | 移动端基础好，需完善流程 |
| **第三方仓储** | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 客户门户和计费需加强 |
| **库位精细化管理** | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 容量管理和智能分配缺失 |
| **质量管理** | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 质检流程需要完善 |
| **报表分析** | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | BI分析功能严重不足 |
| **系统集成** | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | API和EDI能力需加强 |
| **成本优势** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐ | ⭐ | 开源免费是最大优势 |

### 🎯 竞争优势与劣势分析

**✅ 核心优势：**
1. **开源免费**：相比商业软件节省80%以上成本
2. **移动端原生支持**：PDA和移动APP功能完善
3. **技术栈现代化**：便于二次开发和定制
4. **化工行业专业性**：已有化工模块基础
5. **多语言国际化**：支持全球化部署

**⚠️ 主要劣势：**
1. **批号管理薄弱**：这是WMS的核心功能
2. **报表分析不足**：缺少BI和数据分析能力
3. **系统集成能力有限**：与ERP、财务系统集成困难
4. **客户门户缺失**：第三方仓储必需功能
5. **智能化程度低**：缺少AI和自动化功能

## 📋 整改方案与实施路线图

### 🚨 紧急优先级（1-2个月）- 核心功能补强

#### 1. 批号管理系统完善
```
优先级：🔴 极高
预估工时：150工时
投入成本：30万元
预期ROI：400%

实施计划：
Week 1-2: 数据模型重构，增加批号字段到所有相关表
Week 3-4: 批号生成规则和FIFO/FEFO算法开发
Week 5-6: 批号追踪界面和API开发
Week 7-8: 移动端批号扫码功能开发
```

#### 2. 塑料颗粒专业功能
```
优先级：🔴 极高
预估工时：120工时
投入成本：24万元
预期ROI：300%

实施计划：
Week 1-2: 塑料颗粒规格参数模型设计
Week 3-4: 环境监控（湿度、温度）功能开发
Week 5-6: 包装完整性检查流程开发
Week 7-8: 质量证书管理功能开发
```

### ⚡ 高优先级（2-4个月）- 用户体验提升

#### 3. UI/UX设计全面改版
```
优先级：🔴 极高
预估工时：180工时
投入成本：36万元
预期ROI：300%

实施计划：
Month 1: UI设计规范制定，设计系统建立
Month 2: 后台管理界面重新设计开发
Month 3: 移动端APP界面重新设计开发
Month 4: 用户体验测试和优化调整
```

**🎨 UI设计改进重点：**

**后台管理界面改进：**
- 采用现代化的Material Design或Ant Design设计语言
- 重新设计导航结构，提升操作效率
- 优化数据表格展示，增加筛选和排序功能
- 改进表单设计，减少用户输入错误
- 增加数据可视化图表，提升数据洞察能力

**移动端APP界面改进：**
- 采用原生移动端设计规范（iOS Human Interface Guidelines / Material Design）
- 优化扫码界面，提升扫码成功率和用户体验
- 重新设计任务列表和操作流程界面
- 增加手势操作支持，提升操作便捷性
- 优化离线模式界面提示和数据同步状态

**设计系统建立：**
```scss
// 设计系统核心变量
$primary-color: #1976D2;      // 主色调：专业蓝
$secondary-color: #FFC107;    // 辅助色：警示黄
$success-color: #4CAF50;      // 成功色：绿色
$warning-color: #FF9800;      // 警告色：橙色
$error-color: #F44336;        // 错误色：红色

// 字体系统
$font-family-primary: 'PingFang SC', 'Microsoft YaHei', sans-serif;
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;

// 间距系统
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 圆角系统
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;

// 阴影系统
$shadow-sm: 0 1px 3px rgba(0,0,0,0.12);
$shadow-md: 0 4px 6px rgba(0,0,0,0.12);
$shadow-lg: 0 10px 15px rgba(0,0,0,0.12);
```

#### 4. 无纸化办公系统
```
优先级：🟡 高
预估工时：200工时
投入成本：40万元
预期ROI：250%

实施计划：
Month 1: 电子签名和拍照组件开发
Month 2: 工作流引擎和模板系统开发
Month 3: 移动端无纸化界面开发
Month 4: 电子单据生成和归档系统
```

#### 4. 客户门户系统
```
优先级：🟡 高
预估工时：180工时
投入成本：36万元
预期ROI：300%

实施计划：
Month 1: 客户权限管理系统开发
Month 2: 客户库存视图和API开发
Month 3: 客户门户前端界面开发
Month 4: 报表下载和预警功能开发
```

#### 5. 库位精细化管理
```
优先级：🟡 高
预估工时：100工时
投入成本：20万元
预期ROI：200%

实施计划：
Month 1: 库位容量管理功能开发
Month 2: 智能库位分配算法开发
Month 3: 库位可视化界面开发
Month 4: 库位利用率分析功能
```

### 🔄 中等优先级（4-6个月）- 系统完善

#### 6. 报表分析系统
```
优先级：🟢 中等
预估工时：150工时
投入成本：30万元
预期ROI：150%

功能清单：
- 库存分析报表
- 作业效率分析
- 成本分析功能
- KPI仪表板
- 数据导出功能
```

#### 7. 质量管理系统
```
优先级：🟢 中等
预估工时：120工时
投入成本：24万元
预期ROI：180%

功能清单：
- 质检任务管理
- 质检结果录入
- 不合格品处理
- 质量追溯功能
- 质量统计分析
```

### 🔮 长期规划（6-12个月）- 智能化升级

#### 8. 系统集成能力
```
优先级：🔵 长期
预估工时：200工时
投入成本：40万元

功能清单：
- 标准API接口开发
- EDI数据交换
- ERP集成模块
- 财务系统接口
- 第三方物流接口
```

#### 9. AI智能化功能
```
优先级：🔵 长期
预估工时：300工时
投入成本：60万元

功能清单：
- 需求预测算法
- 智能补货建议
- 异常检测功能
- 智能调度优化
- 机器学习分析
```

## 💰 投入产出分析

### 总体投资规划

| 阶段 | 投入成本 | 预期收益 | ROI | 完成时间 |
|------|---------|---------|-----|----------|
| 紧急优先级 | 54万元 | 189万元 | 350% | 2个月 |
| 高优先级 | 96万元 | 240万元 | 250% | 4个月 |
| 中等优先级 | 54万元 | 89万元 | 165% | 6个月 |
| 长期规划 | 100万元 | 150万元 | 150% | 12个月 |
| **总计** | **304万元** | **668万元** | **220%** | **12个月** |

### 成本效益分析

**相比商业WMS的成本优势：**
- SAP WM许可费用：200-500万元/年
- 金蝶云星空：50-150万元/年
- 用友U8+：80-200万元/年
- **GreaterWMS改进总投入：304万元（一次性）**

**预期收益来源：**
1. **作业效率提升30%**：年节省人工成本120万元
2. **库存准确率提升至99.5%**：减少损失80万元/年
3. **客户满意度提升**：增加业务收入200万元/年
4. **无纸化办公**：年节省纸质成本和管理费用50万元

## 📊 最终评估与建议

### 🎯 系统综合评分

| 评估维度 | 当前评分 | 改进后评分 | 市场标杆 |
|---------|---------|-----------|----------|
| **功能完整性** | 65/100 | 90/100 | 95/100 |
| **丙类货物专业性** | 60/100 | 95/100 | 85/100 |
| **无纸化程度** | 70/100 | 95/100 | 90/100 |
| **第三方仓储能力** | 40/100 | 85/100 | 90/100 |
| **用户体验** | 75/100 | 90/100 | 92/100 |
| **技术先进性** | 85/100 | 90/100 | 88/100 |
| **成本优势** | 95/100 | 95/100 | 60/100 |
| **可扩展性** | 80/100 | 85/100 | 85/100 |
| **综合评分** | **71/100** | **89/100** | **87/100** |

### 🏆 核心竞争力分析

**改进后的核心优势：**
1. **丙类货物专业化领先**：超越市场同类产品
2. **塑料颗粒精细化管理**：行业最佳实践
3. **全流程无纸化**：移动端体验优秀
4. **开源免费**：成本优势巨大
5. **快速定制能力**：技术架构现代化

**仍需持续改进的方面：**
1. **报表分析能力**：需要BI增强
2. **系统集成能力**：API标准化
3. **智能化水平**：AI功能缺失

### 📋 实施建议

#### 1. 立即行动项（本月内）
- [ ] 成立项目改进小组，配置5-8名开发人员
- [ ] 制定详细的开发计划和里程碑
- [ ] 搭建测试环境，准备数据迁移方案
- [ ] 与业务部门确认需求细节

#### 2. 短期目标（3个月内）
- [ ] 完成批号管理系统重构
- [ ] 实现塑料颗粒专业功能
- [ ] 上线无纸化办公基础功能
- [ ] 开发客户门户MVP版本

#### 3. 中期目标（6个月内）
- [ ] 完善库位精细化管理
- [ ] 建设完整的报表分析系统
- [ ] 实现全业务流程无纸化
- [ ] 客户门户功能完善

#### 4. 长期目标（12个月内）
- [ ] 系统集成能力建设
- [ ] AI智能化功能开发
- [ ] 行业解决方案标准化
- [ ] 生态合作伙伴建设

### 🎯 成功关键因素

1. **管理层支持**：确保资源投入和决策支持
2. **业务参与**：业务部门深度参与需求定义
3. **技术团队**：配置有经验的开发团队
4. **分阶段实施**：避免大爆炸式上线风险
5. **用户培训**：确保用户能够熟练使用新功能
6. **持续优化**：建立反馈机制和持续改进流程

### 📈 预期成果

**12个月后预期达成：**
- ✅ 功能完整性达到市场领先水平（90分以上）
- ✅ 丙类货物和塑料颗粒管理行业最佳实践
- ✅ 全业务流程100%无纸化办公
- ✅ 第三方仓储客户满意度95%以上
- ✅ 系统作业效率提升30%以上
- ✅ 库存准确率达到99.5%以上
- ✅ 年度ROI达到220%以上

## 📝 结论

GreaterWMS作为一个开源WMS系统，在技术架构和基础功能方面已经具备了良好的基础，特别是在移动端支持和多仓库管理方面表现出色。针对丙类货物和塑料颗粒的管理需求，以及全业务无纸化办公的目标，系统需要在以下几个方面进行重点改进：

### 🎯 核心改进重点

1. **批号管理系统**：这是影响系统实用性的最关键因素
2. **塑料颗粒专业功能**：包括规格管理、环境监控、质量控制
3. **无纸化工作流**：电子签名、移动端操作、文档电子化
4. **客户门户系统**：第三方仓储的核心需求
5. **库位精细化管理**：容量控制、智能分配、可视化

### 💡 战略建议

通过分阶段的系统改进，GreaterWMS有望在12个月内成为丙类货物和塑料颗粒管理领域的领先解决方案。其开源免费的成本优势，结合专业化的功能设计，将在中小企业市场具有强大的竞争力。

建议企业将此次系统改进作为数字化转型的重要项目，投入必要的资源，确保项目成功实施，最终实现业务效率的显著提升和管理水平的全面升级。

---

**报告编制：** CodeBuddy AI助手  
**报告日期：** 2025年8月23日  
**版本：** v2.0（修订版）  
**适用范围：** 丙类货物、塑料颗粒、第三方仓储管理
# GreaterWMS 仓储管理系统功能完整性审查报告（修订版）

## 📋 项目概述

GreaterWMS是一个基于Django + Vue的开源仓储管理系统，支持多仓库管理、扫描PDA、移动APP等功能。该系统已针对化工行业进行了专门的功能增强，特别适合**丙类货物和塑料颗粒**等非危化品的精细化管理，具备较为完整的WMS核心功能。

**技术架构：**
- 后端：Django 4.1.2 + Django REST Framework
- 前端：Vue 2.6.0 + Quasar Framework
- 移动端：Cordova跨平台应用
- 数据库：SQLite(开发) / PostgreSQL(生产)

**业务特点：**
- 主要管理丙类货物（非危化品）
- 塑料颗粒等化工原料的精细化管理
- 全业务流程无纸化办公
- 多仓库数据隔离和权限管控

## 🎯 丙类货物与塑料颗粒管理特点分析

### 丙类货物特征
- **非危险品**：不涉及危化品管理复杂性
- **包装标准化**：通常为袋装、桶装、托盘装
- **批次管理重要**：需要严格的批次追溯
- **质量稳定性好**：相对简单的存储条件要求
- **流转频率高**：需要高效的进出库管理

### 塑料颗粒管理要点
- **颗粒规格管理**：粒径、颜色、牌号等规格参数
- **防潮要求**：需要控制湿度，防止受潮结块
- **批次纯度**：不同批次不能混合，影响产品质量
- **包装完整性**：防止包装破损导致颗粒散落
- **先进先出**：严格按生产日期执行FIFO原则

## 🎯 核心功能模块审查

### 1. 入库管理 (ASN - Advanced Shipping Notice)

**✅ 已实现功能：**
- ASN单据管理（AsnListModel, AsnDetailModel）
- 预收货通知处理
- 供应商信息管理
- 货物预分拣（AsnPreSortViewSet）
- 移库到库位（MoveToBinViewSet）
- 条码打印支持
- 文件导入导出

**⚠️ 针对丙类货物的不足：**
1. **塑料颗粒规格管理缺失**：缺少粒径、熔融指数、颜色等关键参数
2. **包装完整性检查不完善**：没有包装破损检查流程
3. **批次管理不完整**：ASN模型中没有批号字段
4. **湿度监控缺失**：塑料颗粒对湿度敏感，需要环境监控

**🔧 改进建议：**
```python
# 针对丙类货物和塑料颗粒的ASN增强
class AsnDetailModel(models.Model):
    # 现有字段...
    
    # 塑料颗粒专用字段
    batch_number = models.CharField(max_length=100, verbose_name="批次号", blank=True)
    production_date = models.DateField(verbose_name="生产日期", null=True, blank=True)
    expiry_date = models.DateField(verbose_name="过期日期", null=True, blank=True)
    
    # 塑料颗粒规格参数
    particle_size = models.CharField(max_length=50, verbose_name="粒径规格", blank=True)
    melt_index = models.DecimalField(max_digits=8, decimal_places=4, verbose_name="熔融指数", null=True, blank=True)
    color_code = models.CharField(max_length=50, verbose_name="颜色编码", blank=True)
    grade_specification = models.CharField(max_length=100, verbose_name="牌号规格", blank=True)
    
    # 包装检查
    package_condition = models.CharField(max_length=20, choices=[
        ('GOOD', '完好'),
        ('MINOR_DAMAGE', '轻微破损'),
        ('MAJOR_DAMAGE', '严重破损'),
        ('LEAKAGE', '泄漏'),
    ], default='GOOD', verbose_name="包装状态")
    
    # 环境监控
    humidity_on_arrival = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="到货湿度(%)", null=True, blank=True)
    temperature_on_arrival = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="到货温度(℃)", null=True, blank=True)
    
    # 质量状态
    quality_status = models.CharField(max_length=20, choices=[
        ('PENDING', '待检'),
        ('QUALIFIED', '合格'),
        ('UNQUALIFIED', '不合格'),
        ('QUARANTINE', '隔离'),
    ], default='PENDING', verbose_name="质检状态")
```

### 2. 出库管理 (DN - Delivery Note)

**✅ 已实现功能：**
- DN单据管理（DnListModel, DnDetailModel）
- 拣货清单管理（PickingListModel）
- 客户信息管理
- 缺货处理（back_order_label）

**⚠️ 针对塑料颗粒的不足：**
1. **批次FIFO控制不完善**：缺少自动按生产日期分配批次
2. **颗粒混批检查缺失**：不同批次颗粒不能混合发货
3. **包装规格匹配缺失**：客户要求的包装规格与库存不匹配时的处理
4. **质量证书管理缺失**：塑料颗粒需要随货提供质量证书

**🔧 改进建议：**
```python
# DN增强模型
class DnDetailEnhancedModel(models.Model):
    # 继承原有DN字段...
    
    # 批次分配
    allocated_batches = models.JSONField(default=list, verbose_name="分配批次")
    fifo_compliance = models.BooleanField(default=True, verbose_name="FIFO合规")
    batch_purity_check = models.BooleanField(default=True, verbose_name="批次纯度检查")
    
    # 包装要求
    required_package_type = models.CharField(max_length=50, verbose_name="要求包装类型", blank=True)
    required_package_weight = models.DecimalField(max_digits=8, decimal_places=3, verbose_name="要求包装重量", null=True, blank=True)
    
    # 质量文档
    quality_certificate_required = models.BooleanField(default=True, verbose_name="需要质量证书")
    quality_certificate_path = models.CharField(max_length=255, verbose_name="质量证书路径", blank=True)
    
    # 特殊要求
    moisture_content_limit = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="水分含量限制(%)", null=True, blank=True)
    special_handling_notes = models.TextField(verbose_name="特殊处理说明", blank=True)
```

### 3. 库存管理 (Stock) - 重点改进

**✅ 已实现功能：**
- 库存汇总管理（StockListModel）
- 库位库存管理（StockBinModel）
- 多种库存状态管理

**🚨 严重不足（影响塑料颗粒管理）：**
1. **批号管理完全缺失**：这是塑料颗粒管理的核心
2. **保质期管理缺失**：塑料颗粒有保质期要求
3. **湿度监控缺失**：影响颗粒质量的关键因素
4. **批次纯度控制缺失**：不同批次混合会影响产品质量

**🔧 重构建议：**
```python
# 重构库存模型以支持塑料颗粒管理
class PlasticGranuleStockModel(models.Model):
    """塑料颗粒库存模型"""
    
    # 基础信息
    goods_code = models.CharField(max_length=255, verbose_name="产品编码")
    goods_desc = models.CharField(max_length=255, verbose_name="产品描述")
    bin_name = models.CharField(max_length=255, verbose_name="库位")
    
    # 批次信息（核心）
    batch_number = models.CharField(max_length=100, verbose_name="批次号")
    production_date = models.DateField(verbose_name="生产日期")
    expiry_date = models.DateField(verbose_name="过期日期", null=True, blank=True)
    manufacturer = models.CharField(max_length=255, verbose_name="生产厂家")
    
    # 塑料颗粒规格
    grade_specification = models.CharField(max_length=100, verbose_name="牌号规格")
    particle_size = models.CharField(max_length=50, verbose_name="粒径", blank=True)
    melt_index = models.DecimalField(max_digits=8, decimal_places=4, verbose_name="熔融指数", null=True, blank=True)
    color_code = models.CharField(max_length=50, verbose_name="颜色编码", blank=True)
    density = models.DecimalField(max_digits=6, decimal_places=3, verbose_name="密度(g/cm³)", null=True, blank=True)
    
    # 数量管理
    total_quantity = models.DecimalField(max_digits=12, decimal_places=3, verbose_name="总数量")
    available_quantity = models.DecimalField(max_digits=12, decimal_places=3, verbose_name="可用数量")
    reserved_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0, verbose_name="预留数量")
    damaged_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0, verbose_name="损坏数量")
    
    # 质量状态
    quality_status = models.CharField(max_length=20, choices=[
        ('AVAILABLE', '可用'),
        ('QUARANTINE', '隔离'),
        ('HOLD', '保留'),
        ('DAMAGED', '损坏'),
        ('EXPIRED', '过期'),
    ], default='AVAILABLE', verbose_name="质量状态")
    
    # 环境监控
    current_humidity = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="当前湿度(%)", null=True, blank=True)
    current_temperature = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="当前温度(℃)", null=True, blank=True)
    last_environment_check = models.DateTimeField(verbose_name="最后环境检查时间", null=True, blank=True)
    
    # 包装信息
    package_type = models.CharField(max_length=50, verbose_name="包装类型")
    package_weight = models.DecimalField(max_digits=8, decimal_places=3, verbose_name="包装重量(kg)")
    package_count = models.IntegerField(verbose_name="包装件数")
    package_condition = models.CharField(max_length=20, default='GOOD', verbose_name="包装状态")
    
    # 成本信息
    unit_cost = models.DecimalField(max_digits=12, decimal_places=4, verbose_name="单位成本")
    total_value = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="总价值")
    
    # 移动历史
    last_movement_type = models.CharField(max_length=20, verbose_name="最后移动类型", blank=True)
    last_movement_date = models.DateTimeField(verbose_name="最后移动时间", null=True, blank=True)
    
    # 预警设置
    low_stock_threshold = models.DecimalField(max_digits=12, decimal_places=3, verbose_name="低库存阈值", default=0)
    expiry_alert_days = models.IntegerField(default=30, verbose_name="过期预警天数")
    
    # 通用字段
    openid = models.CharField(max_length=255, verbose_name="租户ID")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        db_table = 'plastic_granule_stock'
        verbose_name = '塑料颗粒库存'
        unique_together = ['goods_code', 'batch_number', 'bin_name', 'openid']
        indexes = [
            models.Index(fields=['goods_code', 'batch_number']),
            models.Index(fields=['expiry_date']),
            models.Index(fields=['quality_status']),
            models.Index(fields=['production_date']),
        ]

    @property
    def days_to_expiry(self):
        """距离过期天数"""
        if self.expiry_date:
            from django.utils import timezone
            delta = self.expiry_date - timezone.now().date()
            return delta.days
        return None

    @property
    def is_near_expiry(self):
        """是否临近过期"""
        days = self.days_to_expiry
        return days is not None and days <= self.expiry_alert_days

    @property
    def utilization_rate(self):
        """库存利用率"""
        if self.total_quantity > 0:
            return (self.available_quantity / self.total_quantity) * 100
        return 0
```

### 4. 库位管理 (BinSet) - 针对塑料颗粒优化

**✅ 已实现功能：**
- 库位基础信息管理
- 库位尺寸和属性管理
- 空库位标识

**⚠️ 塑料颗粒存储不足：**
1. **湿度控制区域划分缺失**：塑料颗粒需要低湿度环境
2. **重量容量限制缺失**：颗粒密度高，需要重量限制
3. **通风要求管理缺失**：防止颗粒受潮需要通风
4. **批次隔离要求缺失**：不同批次需要物理隔离

**🔧 改进建议：**
```python
class PlasticGranuleBinModel(models.Model):
    """塑料颗粒专用库位模型"""
    
    # 继承基础库位信息
    bin_name = models.CharField(max_length=255, unique=True, verbose_name="库位编码")
    bin_zone = models.CharField(max_length=50, verbose_name="库位区域")
    
    # 物理属性
    length = models.DecimalField(max_digits=8, decimal_places=3, verbose_name="长度(m)")
    width = models.DecimalField(max_digits=8, decimal_places=3, verbose_name="宽度(m)")
    height = models.DecimalField(max_digits=8, decimal_places=3, verbose_name="高度(m)")
    volume_capacity = models.DecimalField(max_digits=10, decimal_places=3, verbose_name="体积容量(m³)")
    weight_capacity = models.DecimalField(max_digits=10, decimal_places=3, verbose_name="重量容量(kg)")
    
    # 环境控制
    humidity_controlled = models.BooleanField(default=False, verbose_name="湿度控制")
    target_humidity = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="目标湿度(%)", null=True, blank=True)
    ventilation_required = models.BooleanField(default=False, verbose_name="通风要求")
    temperature_controlled = models.BooleanField(default=False, verbose_name="温度控制")
    
    # 存储限制
    single_batch_only = models.BooleanField(default=True, verbose_name="单批次存储")
    max_batch_age_days = models.IntegerField(verbose_name="最大批次存储天数", null=True, blank=True)
    compatible_materials = models.JSONField(default=list, verbose_name="兼容材料类型")
    
    # 当前状态
    current_weight = models.DecimalField(max_digits=10, decimal_places=3, default=0, verbose_name="当前重量")
    current_volume = models.DecimalField(max_digits=10, decimal_places=3, default=0, verbose_name="当前体积")
    current_batch = models.CharField(max_length=100, verbose_name="当前批次", blank=True)
    current_material = models.CharField(max_length=255, verbose_name="当前物料", blank=True)
    
    # 库位状态
    bin_status = models.CharField(max_length=20, choices=[
        ('AVAILABLE', '可用'),
        ('OCCUPIED', '占用'),
        ('RESERVED', '预留'),
        ('MAINTENANCE', '维护中'),
        ('BLOCKED', '锁定'),
    ], default='AVAILABLE', verbose_name="库位状态")
    
    # 设备信息
    humidity_sensor_id = models.CharField(max_length=50, verbose_name="湿度传感器ID", blank=True)
    temperature_sensor_id = models.CharField(max_length=50, verbose_name="温度传感器ID", blank=True)
    ventilation_system_id = models.CharField(max_length=50, verbose_name="通风系统ID", blank=True)
    
    @property
    def weight_utilization(self):
        """重量利用率"""
        if self.weight_capacity > 0:
            return (self.current_weight / self.weight_capacity) * 100
        return 0

    @property
    def volume_utilization(self):
        """体积利用率"""
        if self.volume_capacity > 0:
            return (self.current_volume / self.volume_capacity) * 100
        return 0

    @property
    def is_batch_pure(self):
        """批次纯度检查"""
        if self.single_batch_only and self.current_batch:
            # 检查库位中是否只有一个批次
            from .models import PlasticGranuleStockModel
            batches = PlasticGranuleStockModel.objects.filter(
                bin_name=self.bin_name
            ).values_list('batch_number', flat=True).distinct()
            return len(batches) <= 1
        return True
```

## 📱 全业务无纸化办公设计方案

### 🎯 无纸化办公核心目标

1. **消除纸质单据**：所有业务单据电子化
2. **移动端操作**：现场作业全部通过移动设备完成
3. **电子签名确认**：关键节点电子签名替代手工签字
4. **实时数据同步**：所有操作实时更新到系统
5. **电子档案管理**：文档电子化存储和检索

### 📋 无纸化业务流程设计

#### 1. 入库无纸化流程

```mermaid
graph TD
    A[供应商发货通知] --> B[电子ASN创建]
    B --> C[司机移动端确认到货]
    C --> D[收货员PDA扫码验收]
    D --> E[移动端拍照记录]
    E --> F[电子签名确认]
    F --> G[自动生成入库单]
    G --> H[质检员移动端质检]
    H --> I[电子质检报告]
    I --> J[自动上架指令]
    J --> K[PDA扫码上架确认]
```

**技术实现：**
```python
class PaperlessReceiptWorkflow(models.Model):
    """无纸化收货工作流"""
    
    workflow_id = models.CharField(max_length=50, unique=True, verbose_name="工作流ID")
    asn_code = models.CharField(max_length=255, verbose_name="ASN编号")
    
    # 工作流步骤
    steps = models.JSONField(default=list, verbose_name="工作流步骤")
    current_step = models.IntegerField(default=0, verbose_name="当前步骤")
    
    # 电子签名记录
    signatures = models.JSONField(default=list, verbose_name="电子签名记录")
    
    # 照片记录
    photos = models.JSONField(default=list, verbose_name="照片记录")
    
    # 扫码记录
    scan_records = models.JSONField(default=list, verbose_name="扫码记录")
    
    # 移动端操作记录
    mobile_operations = models.JSONField(default=list, verbose_name="移动端操作")
    
    # 状态管理
    status = models.CharField(max_length=20, choices=[
        ('STARTED', '已开始'),
        ('IN_PROGRESS', '进行中'),
        ('COMPLETED', '已完成'),
        ('EXCEPTION', '异常'),
    ], default='STARTED', verbose_name="状态")
    
    def add_step_completion(self, step_name, operator, signature_data=None, photos=None, scan_data=None):
        """添加步骤完成记录"""
        completion_record = {
            'step_name': step_name,
            'operator': operator,
            'timestamp': timezone.now().isoformat(),
            'signature': signature_data,
            'photos': photos or [],
            'scan_data': scan_data,
        }
        
        if not self.steps:
            self.steps = []
        self.steps.append(completion_record)
        self.current_step += 1
        self.save()
```

#### 2. 出库无纸化流程

```mermaid
graph TD
    A[客户订单] --> B[电子DN生成]
    B --> C[系统自动分配批次]
    C --> D[生成拣货任务]
    D --> E[拣货员PDA接收任务]
    E --> F[扫码拣货确认]
    F --> G[移动端拍照记录]
    G --> H[质检员移动端复核]
    H --> I[包装员扫码包装]
    I --> J[司机电子签名提货]
    J --> K[自动生成出库单]
```

#### 3. 盘点无纸化流程

```mermaid
graph TD
    A[系统生成盘点计划] --> B[移动端推送盘点任务]
    B --> C[盘点员PDA扫码盘点]
    C --> D[实时录入盘点数据]
    D --> E[移动端拍照异常]
    E --> F[主管移动端审核]
    F --> G[电子签名确认]
    G --> H[自动生成盘点报告]
```

### 📱 移动端功能模块设计

#### 1. 移动端核心功能架构

```javascript
// 移动端应用架构
src/
├── pages/
│   ├── Receipt/              // 收货模块
│   │   ├── ReceiptScan.vue   // 扫码收货
│   │   ├── QualityCheck.vue  // 质量检查
│   │   ├── PhotoCapture.vue  // 拍照记录
│   │   └── SignatureCapture.vue // 电子签名
│   ├── Shipping/             // 发货模块
│   │   ├── PickingTask.vue   // 拣货任务
│   │   ├── PackingConfirm.vue // 包装确认
│   │   └── LoadingConfirm.vue // 装车确认
│   ├── Inventory/            // 库存模块
│   │   ├── CycleCount.vue    // 循环盘点
│   │   ├── StockTransfer.vue // 库存移动
│   │   └── StockInquiry.vue  // 库存查询
│   └── Common/               // 通用模块
│       ├── Scanner.vue       // 扫码组件
│       ├── Camera.vue        // 拍照组件
│       ├── Signature.vue     // 签名组件
│       └── OfflineSync.vue   // 离线同步
```

#### 2. 关键移动端组件实现

**扫码组件：**
```vue
<template>
  <div class="scanner-container">
    <qr-scanner
      @decode="onDecode"
      @init="onInit"
      :track="trackFunctionSelected"
    />
    <div class="scan-result">
      <q-input
        v-model="scanResult"
        label="扫码结果"
        readonly
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'UniversalScanner',
  data() {
    return {
      scanResult: '',
      scanHistory: []
    }
  },
  methods: {
    onDecode(result) {
      this.scanResult = result
      this.processScanResult(result)
    },
    
    processScanResult(result) {
      // 解析不同类型的条码
      const barcodeType = this.identifyBarcodeType(result)
      
      switch(barcodeType) {
        case 'GOODS_CODE':
          this.handleGoodsCodeScan(result)
          break
        case 'BATCH_NUMBER':
          this.handleBatchNumberScan(result)
          break
        case 'BIN_LOCATION':
          this.handleBinLocationScan(result)
          break
        case 'ASN_CODE':
          this.handleAsnCodeScan(result)
          break
        default:
          this.handleUnknownScan(result)
      }
    },
    
    identifyBarcodeType(barcode) {
      // 根据条码格式识别类型
      if (barcode.startsWith('GC-')) return 'GOODS_CODE'
      if (barcode.startsWith('BN-')) return 'BATCH_NUMBER'
      if (barcode.startsWith('BL-')) return 'BIN_LOCATION'
      if (barcode.startsWith('ASN-')) return 'ASN_CODE'
      return 'UNKNOWN'
    }
  }
}
</script>
```

**电子签名组件：**
```vue
<template>
  <div class="signature-container">
    <canvas
      ref="signatureCanvas"
      @touchstart="startDrawing"
      @touchmove="draw"
      @touchend="stopDrawing"
      class="signature-canvas"
    />
    <div class="signature-actions">
      <q-btn @click="clearSignature" label="清除" />
      <q-btn @click="saveSignature" label="保存" color="primary" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElectronicSignature',
  data() {
    return {
      isDrawing: false,
      canvas: null,
      ctx: null
    }
  },
  mounted() {
    this.initCanvas()
  },
  methods: {
    initCanvas() {
      this.canvas = this.$refs.signatureCanvas
      this.ctx = this.canvas.getContext('2d')
      this.ctx.strokeStyle = '#000000'
      this.ctx.lineWidth = 2
      this.ctx.lineCap = 'round'
    },
    
    startDrawing(event) {
      this.isDrawing = true
      const rect = this.canvas.getBoundingClientRect()
      const x = event.touches[0].clientX - rect.left
      const y = event.touches[0].clientY - rect.top
      this.ctx.beginPath()
      this.ctx.moveTo(x, y)
    },
    
    draw(event) {
      if (!this.isDrawing) return
      const rect = this.canvas.getBoundingClientRect()
      const x = event.touches[0].clientX - rect.left
      const y = event.touches[0].clientY - rect.top
      this.ctx.lineTo(x, y)
      this.ctx.stroke()
    },
    
    stopDrawing() {
      this.isDrawing = false
    },
    
    saveSignature() {
      const signatureData = this.canvas.toDataURL()
      this.$emit('signature-saved', {
        data: signatureData,
        timestamp: new Date().toISOString(),
        operator: this.$store.state.user.name
      })
    },
    
    clearSignature() {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
    }
  }
}
</script>
```

### 📊 电子单据系统设计

#### 1. 电子单据模板引擎

```python
class ElectronicDocumentTemplate(models.Model):
    """电子