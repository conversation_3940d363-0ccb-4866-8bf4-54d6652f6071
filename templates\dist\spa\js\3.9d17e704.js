(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[3],{2127:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e._self._c;return t("div",[t("transition",{attrs:{appear:"","enter-active-class":"animated fadeIn"}},[t("q-table",{staticClass:"my-sticky-header-column-table shadow-24",attrs:{data:e.table_list,"row-key":"id",separator:e.separator,loading:e.loading,filter:e.filter,columns:e.columns,"hide-bottom":"",pagination:e.pagination,"no-data-label":"No data","no-results-label":"No data you want","table-style":{height:e.height},flat:"",bordered:""},on:{"update:pagination":function(t){e.pagination=t}},scopedSlots:e._u([{key:"top",fn:function(){return[t("q-btn-group",{attrs:{push:""}},[t("q-btn",{attrs:{label:e.$t("new"),icon:"add"},on:{click:function(t){return e.newFormCheck()}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n             "+e._s(e.$t("newtip"))+"\n           ")])],1),t("q-btn",{attrs:{label:e.$t("refresh"),icon:"refresh"},on:{click:function(t){return e.reFresh()}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n             "+e._s(e.$t("refreshtip"))+"\n           ")])],1)],1)]},proxy:!0},{key:"body",fn:function(a){return[t("q-tr",{attrs:{props:a}},[a.row.id===e.editid?[t("q-td",{key:"company_name",attrs:{props:a}},[t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("baseinfo.view_company.company_name"),autofocus:"",rules:[e=>e&&e.length>0||"Please Enter The Company Name"]},model:{value:e.editFormData.company_name,callback:function(t){e.$set(e.editFormData,"company_name",t)},expression:"editFormData.company_name"}})],1)]:a.row.id!==e.editid?[t("q-td",{key:"company_name",attrs:{props:a}},[e._v("\n             "+e._s(a.row.company_name)+"\n           ")])]:e._e(),a.row.id===e.editid?[t("q-td",{key:"company_city",attrs:{props:a}},[t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("baseinfo.view_company.company_city"),rules:[e=>e&&e.length>0||"Please Enter The Company City"]},model:{value:e.editFormData.company_city,callback:function(t){e.$set(e.editFormData,"company_city",t)},expression:"editFormData.company_city"}})],1)]:a.row.id!==e.editid?[t("q-td",{key:"company_city",attrs:{props:a}},[e._v("\n             "+e._s(a.row.company_city)+"\n           ")])]:e._e(),a.row.id===e.editid?[t("q-td",{key:"company_address",attrs:{props:a}},[t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("baseinfo.view_company.company_address"),rules:[e=>e&&e.length>0||"Please Enter The Company Address"]},model:{value:e.editFormData.company_address,callback:function(t){e.$set(e.editFormData,"company_address",t)},expression:"editFormData.company_address"}})],1)]:a.row.id!==e.editid?[t("q-td",{key:"company_address",attrs:{props:a}},[e._v("\n             "+e._s(a.row.company_address)+"\n           ")])]:e._e(),a.row.id===e.editid?[t("q-td",{key:"company_contact",attrs:{props:a}},[t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("baseinfo.view_company.company_contact"),rules:[e=>e&&e.length>0||"Please Enter The Company Contact"]},model:{value:e.editFormData.company_contact,callback:function(t){e.$set(e.editFormData,"company_contact",t)},expression:"editFormData.company_contact"}})],1)]:a.row.id!==e.editid?[t("q-td",{key:"company_contact",attrs:{props:a}},[e._v("\n             "+e._s(a.row.company_contact)+"\n           ")])]:e._e(),a.row.id===e.editid?[t("q-td",{key:"company_manager",attrs:{props:a}},[t("q-input",{attrs:{dense:"",outlined:"",square:"",label:"Company Manager",rules:[e=>e&&e.length>0||"Please Enter The Company Manager"]},model:{value:e.editFormData.company_manager,callback:function(t){e.$set(e.editFormData,"company_manager",t)},expression:"editFormData.company_manager"}})],1)]:a.row.id!==e.editid?[t("q-td",{key:"company_manager",attrs:{props:a}},[e._v("\n             "+e._s(a.row.company_manager)+"\n           ")])]:e._e(),t("q-td",{key:"creater",attrs:{props:a}},[e._v("\n           "+e._s(a.row.creater)+"\n         ")]),t("q-td",{key:"create_time",attrs:{props:a}},[e._v("\n           "+e._s(a.row.create_time)+"\n         ")]),t("q-td",{key:"update_time",attrs:{props:a}},[e._v("\n           "+e._s(a.row.update_time)+"\n         ")]),e.editMode?e.editMode?[a.row.id===e.editid?[t("q-td",{key:"action",staticStyle:{width:"100px"},attrs:{props:a}},[t("q-btn",{attrs:{round:"",flat:"",push:"",color:"secondary",icon:"check"},on:{click:function(t){return e.editDataSubmit()}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n                 "+e._s(e.$t("confirmedit"))+"\n              ")])],1),t("q-btn",{attrs:{round:"",flat:"",push:"",color:"red",icon:"close"},on:{click:function(t){return e.editDataCancel()}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n                "+e._s(e.$t("canceledit"))+"\n              ")])],1)],1)]:a.row.id!==e.editid?void 0:e._e()]:e._e():[t("q-td",{key:"action",staticStyle:{width:"100px"},attrs:{props:a}},[t("q-btn",{attrs:{round:"",flat:"",push:"",color:"purple",icon:"edit"},on:{click:function(t){return e.editData(a.row)}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n                 "+e._s(e.$t("edit"))+"\n              ")])],1),t("q-btn",{attrs:{round:"",flat:"",push:"",color:"dark",icon:"delete"},on:{click:function(t){return e.deleteData(a.row.id)}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n                "+e._s(e.$t("delete"))+"\n              ")])],1)],1)]],2)]}}])})],1),[t("div",{directives:[{name:"show",rawName:"v-show",value:0!==e.max,expression:"max !== 0"}],staticClass:"q-pa-lg flex flex-center"},[t("div",[e._v(e._s(e.total)+" ")]),t("q-pagination",{attrs:{color:"black",max:e.max,"max-pages":6,"boundary-links":""},on:{click:function(t){return e.getList()}},model:{value:e.current,callback:function(t){e.current=t},expression:"current"}}),t("div",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.paginationIpt,expression:"paginationIpt"}],staticStyle:{width:"60px","text-align":"center"},domProps:{value:e.paginationIpt},on:{blur:e.changePageEnter,input:function(t){t.target.composing||(e.paginationIpt=t.target.value)}}})])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:0===e.max,expression:"max === 0"}],staticClass:"q-pa-lg flex flex-center"},[t("q-btn",{attrs:{flat:"",push:"",color:"dark",label:e.$t("no_data")}})],1)],t("q-dialog",{model:{value:e.newForm,callback:function(t){e.newForm=t},expression:"newForm"}},[t("q-card",{staticClass:"shadow-24"},[t("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[t("div",[e._v(e._s(e.$t("newtip")))]),t("q-space"),t("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[e._v(e._s(e.$t("index.close")))])],1)],1),t("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("baseinfo.view_company.company_name"),autofocus:"",rules:[t=>t&&t.length>0||e.error1]},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.newDataSubmit()}},model:{value:e.newFormData.company_name,callback:function(t){e.$set(e.newFormData,"company_name",t)},expression:"newFormData.company_name"}}),t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("baseinfo.view_company.company_city"),rules:[t=>t&&t.length>0||e.error2]},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.newDataSubmit()}},model:{value:e.newFormData.company_city,callback:function(t){e.$set(e.newFormData,"company_city",t)},expression:"newFormData.company_city"}}),t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("baseinfo.view_company.company_address"),rules:[t=>t&&t.length>0||e.error3]},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.newDataSubmit()}},model:{value:e.newFormData.company_address,callback:function(t){e.$set(e.newFormData,"company_address",t)},expression:"newFormData.company_address"}}),t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("baseinfo.view_company.company_contact"),rules:[t=>t&&t.length>0||e.error4]},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.newDataSubmit()}},model:{value:e.newFormData.company_contact,callback:function(t){e.$set(e.newFormData,"company_contact",t)},expression:"newFormData.company_contact"}}),t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("baseinfo.view_company.company_manager"),rules:[t=>t&&t.length>0||e.error5]},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.newDataSubmit()}},model:{value:e.newFormData.company_manager,callback:function(t){e.$set(e.newFormData,"company_manager",t)},expression:"newFormData.company_manager"}})],1),t("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[t("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(t){return e.newDataCancel()}}},[e._v(e._s(e.$t("cancel")))]),t("q-btn",{attrs:{color:"primary"},on:{click:function(t){return e.newDataSubmit()}}},[e._v(e._s(e.$t("submit")))])],1)],1)],1),t("q-dialog",{model:{value:e.deleteForm,callback:function(t){e.deleteForm=t},expression:"deleteForm"}},[t("q-card",{staticClass:"shadow-24"},[t("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[t("div",[e._v(e._s(e.$t("delete")))]),t("q-space"),t("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[t("q-tooltip",[e._v(e._s(e.$t("index.close")))])],1)],1),t("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[e._v("\n       "+e._s(e.$t("deletetip"))+"\n     ")]),t("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[t("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(t){return e.deleteDataCancel()}}},[e._v(e._s(e.$t("cancel")))]),t("q-btn",{attrs:{color:"primary"},on:{click:function(t){return e.deleteDataSubmit()}}},[e._v(e._s(e.$t("submit")))])],1)],1)],1)],2)},o=[],i=a("3004"),r=a("bd4c"),c=a("a357"),s=a("18d6"),l={name:"Pagecompany",data(){return{openid:"",login_name:"",authin:"0",pathname:"company/",pathname_previous:"",pathname_next:"",separator:"cell",loading:!1,height:"",table_list:[],columns:[{name:"company_name",required:!0,label:this.$t("baseinfo.view_company.company_name"),align:"left",field:"company_name"},{name:"company_city",label:this.$t("baseinfo.view_company.company_city"),field:"company_city",align:"center"},{name:"company_address",label:this.$t("baseinfo.view_company.company_address"),field:"company_address",align:"center"},{name:"company_contact",label:this.$t("baseinfo.view_company.company_contact"),field:"company_contact",align:"center"},{name:"company_manager",label:this.$t("baseinfo.view_company.company_manager"),field:"company_manager",align:"center"},{name:"creater",label:this.$t("creater"),field:"creater",align:"center"},{name:"create_time",label:this.$t("createtime"),field:"create_time",align:"center"},{name:"update_time",label:this.$t("updatetime"),field:"update_time",align:"center"},{name:"action",label:this.$t("action"),align:"right"}],filter:"",pagination:{page:1,rowsPerPage:"30"},newForm:!1,newFormData:{company_name:"",company_city:"",company_address:"",company_contact:"",company_manager:"",creater:""},editid:0,editFormData:{},editMode:!1,deleteForm:!1,deleteid:0,error1:this.$t("baseinfo.view_company.error1"),error2:this.$t("baseinfo.view_company.error2"),error3:this.$t("baseinfo.view_company.error3"),error4:this.$t("baseinfo.view_company.error4"),error5:this.$t("baseinfo.view_company.error5"),current:1,max:0,total:0,paginationIpt:1}},methods:{getList(){var e=this;s["a"].has("auth")&&Object(i["f"])(e.pathname+"?page="+e.current,{}).then((t=>{e.table_list=t.results,e.total=t.count,0===t.count||1===Math.ceil(t.count/30)?e.max=0:e.max=Math.ceil(t.count/30),e.pathname_previous=t.previous,e.pathname_previous=t.previous})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},changePageEnter(e){Number(this.paginationIpt)<1?(this.current=1,this.paginationIpt=1):Number(this.paginationIpt)>this.max?(this.current=this.max,this.paginationIpt=this.max):this.current=Number(this.paginationIpt),this.getList()},getListPrevious(){var e=this;s["a"].has("auth")&&Object(i["f"])(e.pathname_previous,{}).then((t=>{e.table_list=t.results,e.pathname_previous=t.previous,e.pathname_next=t.next})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},getListNext(){var e=this;s["a"].has("auth")&&Object(i["f"])(e.pathname_next,{}).then((t=>{e.table_list=t.results,e.pathname_previous=t.previous,e.pathname_next=t.next})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},reFresh(){var e=this;e.getList()},newFormCheck(){var e=this;e.table_list.length>=1?e.$q.notify({message:"You Just Can Create 1 Line Data",icon:"close",color:"negative"}):e.newForm=!0},newDataSubmit(){var e=this,t=[];e.table_list.forEach((e=>{t.push(e.company_name)})),-1===t.indexOf(e.newFormData.company_name)&&0!==e.newFormData.company_name.length?(e.newFormData.creater=e.login_name,Object(i["i"])(e.pathname,e.newFormData).then((t=>{e.getList(),e.newDataCancel(),e.$q.notify({message:"Success Create",icon:"check",color:"green"})})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))):-1!==t.indexOf(e.newFormData.company_name)?e.$q.notify({message:e.$t("notice.baseinfoerror.companyerror"),icon:"close",color:"negative"}):0===e.newFormData.company_name.length&&e.$q.notify({message:e.$t("baseinfo.view_company.error1"),icon:"close",color:"negative"}),t=[]},newDataCancel(){var e=this;e.newForm=!1,e.newFormData={company_name:"",company_city:"",company_address:"",company_contact:"",company_manager:"",creater:""}},editData(e){var t=this;t.editMode=!0,t.editid=e.id,t.editFormData={company_name:e.company_name,company_city:e.company_city,company_address:e.company_address,company_contact:e.company_contact,company_manager:e.company_manager,creater:t.login_name}},editDataSubmit(){var e=this;Object(i["j"])(e.pathname+e.editid+"/",e.editFormData).then((t=>{e.editDataCancel(),e.getList(),e.$q.notify({message:"Success Edit Data",icon:"check",color:"green"})})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},editDataCancel(){var e=this;e.editMode=!1,e.editid=0,e.editFormData={company_name:"",company_city:"",company_address:"",company_contact:"",company_manager:"",creater:""}},deleteData(e){var t=this;t.deleteForm=!0,t.deleteid=e},deleteDataSubmit(){var e=this;Object(i["d"])(e.pathname+e.deleteid+"/").then((t=>{e.deleteDataCancel(),e.getList(),e.$q.notify({message:"Success Edit Data",icon:"check",color:"green"})})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},deleteDataCancel(){var e=this;e.deleteForm=!1,e.deleteid=0},downloadData(){var e=this;Object(i["g"])(e.pathname+"file/?lang="+s["a"].getItem("lang")).then((t=>{var a=Date.now(),n=r["b"].formatDate(a,"YYYYMMDDHHmmssSSS");const o=Object(c["a"])(e.pathname+n+".csv","\ufeff"+t.data,"text/csv");!0!==o&&e.$q.notify({message:"Browser denied file download...",color:"negative",icon:"warning"})}))}},created(){var e=this;s["a"].has("openid")?e.openid=s["a"].getItem("openid"):(e.openid="",s["a"].set("openid","")),s["a"].has("login_name")?e.login_name=s["a"].getItem("login_name"):(e.login_name="",s["a"].set("login_name","")),s["a"].has("auth")?(e.authin="1",e.getList()):e.authin="0"},mounted(){var e=this;this.$q.platform.is.electron?e.height=String(e.$q.screen.height-290)+"px":e.height=e.$q.screen.height-290+"px"},updated(){},destroyed(){}},m=l,p=a("42e1"),d=a("4f83"),u=a("eaac"),y=a("e7a9"),_=a("9c40"),h=a("05c0"),g=a("bd08"),b=a("db86"),f=a("27f9"),w=a("3b16"),v=a("24e8"),x=a("f09f"),k=a("d847"),q=a("2c91"),D=a("a370"),$=a("7f67"),F=a("eebe"),C=a.n(F),S=Object(p["a"])(m,n,o,!1,null,null,null);"function"===typeof d["default"]&&Object(d["default"])(S);t["default"]=S.exports;C()(S,"components",{QTable:u["a"],QBtnGroup:y["a"],QBtn:_["a"],QTooltip:h["a"],QTr:g["a"],QTd:b["a"],QInput:f["a"],QPagination:w["a"],QDialog:v["a"],QCard:x["a"],QBar:k["a"],QSpace:q["a"],QCardSection:D["a"]}),C()(S,"directives",{ClosePopup:$["a"]})},"4f83":function(e,t,a){"use strict";var n=a("8a0b"),o=a.n(n);t["default"]=o.a},"8a0b":function(e,t){}}]);