(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[34],{"8fbe":function(t,e,o){"use strict";var a=o("f248"),s=o.n(a);e["default"]=s.a},e2e3:function(t,e,o){"use strict";o.r(e);var a=function(){var t=this,e=t._self._c;return e("div",[e("transition",{attrs:{appear:"","enter-active-class":"animated fadeIn"}},[e("q-table",{staticClass:"my-sticky-header-column-table shadow-24",attrs:{data:t.table_list,"row-key":"id",separator:t.separator,loading:t.loading,filter:t.filter,columns:t.columns,"hide-bottom":"",pagination:t.pagination,"no-data-label":"No data","no-results-label":"No data you want","table-style":{height:t.height},flat:"",bordered:""},on:{"update:pagination":function(e){t.pagination=e}},scopedSlots:t._u([{key:"top",fn:function(){return[e("q-btn-group",{attrs:{push:""}},[e("q-btn",{attrs:{label:t.$t("new"),icon:"add"},on:{click:function(e){return t.newFormOpen()}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("newtip")))])],1),e("q-btn",{attrs:{label:t.$t("refresh"),icon:"refresh"},on:{click:function(e){return t.reFresh()}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("refreshtip")))])],1),e("q-btn",{attrs:{label:t.$t("release"),icon:"img:statics/outbound/orderrelease.png"},on:{click:function(e){return t.orderreleaseAllData()}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("releaseallorder")))])],1)],1),e("q-space"),e("q-input",{attrs:{outlined:"",rounded:"",dense:"",debounce:"300",color:"primary",placeholder:t.$t("search")},on:{input:function(e){return t.getSearchList()},keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getSearchList()}},scopedSlots:t._u([{key:"append",fn:function(){return[e("q-icon",{attrs:{name:"search"},on:{click:function(e){return t.getSearchList()}}})]},proxy:!0}]),model:{value:t.filter,callback:function(e){t.filter=e},expression:"filter"}})]},proxy:!0},{key:"body",fn:function(o){return[e("q-tr",{attrs:{props:o}},[e("q-td",{key:"dn_code",attrs:{props:o}},[t._v(t._s(o.row.dn_code))]),e("q-td",{key:"dn_status",attrs:{props:o}},[t._v(t._s(o.row.dn_status))]),e("q-td",{key:"total_weight",attrs:{props:o}},[t._v(t._s(o.row.total_weight.toFixed(4)))]),e("q-td",{key:"total_volume",attrs:{props:o}},[t._v(t._s(o.row.total_volume.toFixed(4)))]),e("q-td",{key:"customer",attrs:{props:o}},[t._v(t._s(o.row.customer))]),e("q-td",{key:"creater",attrs:{props:o}},[t._v(t._s(o.row.creater))]),e("q-td",{key:"create_time",attrs:{props:o}},[t._v(t._s(o.row.create_time))]),e("q-td",{key:"update_time",attrs:{props:o}},[t._v(t._s(o.row.update_time))]),e("q-td",{key:"action",staticStyle:{width:"100px"},attrs:{props:o}},[e("q-btn",{attrs:{round:"",flat:"",push:"",color:"info",icon:"visibility"},on:{click:function(e){return t.viewData(o.row)}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("printthisdn")))])],1),e("q-btn",{attrs:{round:"",flat:"",push:"",color:"positive",icon:"img:statics/outbound/order.png"},on:{click:function(e){return t.neworderData(o.row)}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("confirmorder")))])],1),e("q-btn",{attrs:{round:"",flat:"",push:"",color:"positive",icon:"img:statics/outbound/orderrelease.png"},on:{click:function(e){return t.orderreleaseData(o.row)}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("releaseorder")))])],1),e("q-btn",{attrs:{round:"",flat:"",push:"",color:"secondary",icon:"print"},on:{click:function(e){return t.PrintPickingList(o.row)}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("print")))])],1),e("q-btn",{attrs:{round:"",flat:"",push:"",color:"purple",icon:"img:statics/outbound/picked.png"},on:{click:function(e){return t.pickedData(o.row)}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("confirmpicked")))])],1),e("q-btn",{attrs:{round:"",flat:"",push:"",color:"dark",icon:"rv_hookup"},on:{click:function(e){return t.DispatchDN(o.row)}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("dispatch")))])],1),e("q-btn",{attrs:{round:"",flat:"",push:"",color:"info",icon:"img:statics/outbound/receiving.png"},on:{click:function(e){return t.PODData(o.row)}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("outbound.pod")))])],1),e("q-btn",{attrs:{round:"",flat:"",push:"",color:"purple",icon:"edit"},on:{click:function(e){return t.editData(o.row)}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("edit")))])],1),e("q-btn",{attrs:{round:"",flat:"",push:"",color:"dark",icon:"delete"},on:{click:function(e){return t.deleteData(o.row)}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("delete")))])],1)],1),o.row.transportation_fee.detail!==[]?[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e("q-list",t._l(o.row.transportation_fee.detail,(function(o,a){return e("div",{key:a},[e("q-item",{directives:[{name:"ripple",rawName:"v-ripple"}]},[e("q-item-section",[e("q-item-label",[t._v(t._s(o.transportation_supplier))]),e("q-item-label",[t._v(t._s(t.$t("estimate"))+": "+t._s(o.transportation_cost))])],1)],1)],1)})),0)],1)]:t._e()],2)]}}])})],1),[e("div",{directives:[{name:"show",rawName:"v-show",value:0!==t.max,expression:"max !== 0"}],staticClass:"q-pa-lg flex flex-center"},[e("div",[t._v(t._s(t.total)+" ")]),e("q-pagination",{attrs:{color:"black",max:t.max,"max-pages":6,"boundary-links":""},on:{click:function(e){return t.getList()}},model:{value:t.current,callback:function(e){t.current=e},expression:"current"}}),e("div",[e("input",{directives:[{name:"model",rawName:"v-model",value:t.paginationIpt,expression:"paginationIpt"}],staticStyle:{width:"60px","text-align":"center"},domProps:{value:t.paginationIpt},on:{blur:t.changePageEnter,keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.changePageEnter.apply(null,arguments)},input:function(e){e.target.composing||(t.paginationIpt=e.target.value)}}})])],1),e("div",{directives:[{name:"show",rawName:"v-show",value:0===t.max,expression:"max === 0"}],staticClass:"q-pa-lg flex flex-center"},[e("q-btn",{attrs:{flat:"",push:"",color:"dark",label:t.$t("no_data")}})],1)],e("q-dialog",{model:{value:t.newForm,callback:function(e){t.newForm=e},expression:"newForm"}},[e("q-card",{staticClass:"shadow-24"},[e("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[e("div",[t._v(t._s(t.newFormData.dn_code))]),e("q-space"),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[t._v(t._s(t.$t("index.close")))])],1)],1),e("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[e("q-select",{staticStyle:{"margin-bottom":"5px"},attrs:{filled:"","use-input":"","fill-input":"","hide-selected":"","input-debounce":"0",dense:"",outlined:"",square:"",options:t.customer_list,label:t.$t("baseinfo.view_customer.customer_name"),rules:[e=>e&&e.length>0||t.error1]},on:{filter:t.filterFnS,"input-value":t.setModel,keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"Sno-option",fn:function(){return[e("q-item",[e("q-item-section",{staticClass:"text-grey"},[t._v("\n                No Result\n              ")])],1)]},proxy:!0}]),model:{value:t.newFormData.customer,callback:function(e){t.$set(t.newFormData,"customer",e)},expression:"newFormData.customer"}}),e("q-input",{staticStyle:{"margin-bottom":"5px"},attrs:{dense:"",outlined:"",square:"",debounce:"500",type:"number",label:t.$t("stock.view_stocklist.goods_qty")},on:{keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"before",fn:function(){return[e("q-select",{attrs:{dense:"",outlined:"",square:"","use-input":"","hide-selected":"","fill-input":"",label:t.$t("goods.view_goodslist.goods_code"),options:t.options,autofocus:""},on:{focus:function(e){return t.getFocus(1)},"input-value":t.setOptions,filter:t.filterFn,keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"no-option",fn:function(){return[e("q-item",[e("q-item-section",{staticClass:"text-grey"},[t._v("No results")])],1)]},proxy:!0},t.goodsData1.code?{key:"append",fn:function(){return[e("q-icon",{staticClass:"cursor-pointer",attrs:{name:"cancel"},on:{click:function(e){e.stopPropagation(),t.goodsData1.code=""}}})]},proxy:!0}:null],null,!0),model:{value:t.goodsData1.code,callback:function(e){t.$set(t.goodsData1,"code",e)},expression:"goodsData1.code"}})]},proxy:!0}]),model:{value:t.goodsData1.qty,callback:function(e){t.$set(t.goodsData1,"qty",t._n(e))},expression:"goodsData1.qty"}}),e("q-input",{staticStyle:{"margin-bottom":"5px"},attrs:{dense:"",outlined:"",square:"",debounce:"500",type:"number",label:t.$t("stock.view_stocklist.goods_qty")},on:{keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"before",fn:function(){return[e("q-select",{attrs:{dense:"",outlined:"",square:"","use-input":"","hide-selected":"","fill-input":"",label:t.$t("goods.view_goodslist.goods_code"),options:t.options},on:{focus:function(e){return t.getFocus(2)},"input-value":t.setOptions,filter:t.filterFn,keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"no-option",fn:function(){return[e("q-item",[e("q-item-section",{staticClass:"text-grey"},[t._v("No results")])],1)]},proxy:!0},t.goodsData2.code?{key:"append",fn:function(){return[e("q-icon",{staticClass:"cursor-pointer",attrs:{name:"cancel"},on:{click:function(e){e.stopPropagation(),t.goodsData2.code=""}}})]},proxy:!0}:null],null,!0),model:{value:t.goodsData2.code,callback:function(e){t.$set(t.goodsData2,"code",e)},expression:"goodsData2.code"}})]},proxy:!0}]),model:{value:t.goodsData2.qty,callback:function(e){t.$set(t.goodsData2,"qty",t._n(e))},expression:"goodsData2.qty"}}),e("q-input",{staticStyle:{"margin-bottom":"5px"},attrs:{dense:"",outlined:"",square:"",debounce:"500",type:"number",label:t.$t("stock.view_stocklist.goods_qty")},on:{keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"before",fn:function(){return[e("q-select",{attrs:{dense:"",outlined:"",square:"","use-input":"","hide-selected":"","fill-input":"",label:t.$t("goods.view_goodslist.goods_code"),options:t.options},on:{focus:function(e){return t.getFocus(3)},"input-value":t.setOptions,filter:t.filterFn,keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"no-option",fn:function(){return[e("q-item",[e("q-item-section",{staticClass:"text-grey"},[t._v("No results")])],1)]},proxy:!0},t.goodsData3.code?{key:"append",fn:function(){return[e("q-icon",{staticClass:"cursor-pointer",attrs:{name:"cancel"},on:{click:function(e){e.stopPropagation(),t.goodsData3.code=""}}})]},proxy:!0}:null],null,!0),model:{value:t.goodsData3.code,callback:function(e){t.$set(t.goodsData3,"code",e)},expression:"goodsData3.code"}})]},proxy:!0}]),model:{value:t.goodsData3.qty,callback:function(e){t.$set(t.goodsData3,"qty",t._n(e))},expression:"goodsData3.qty"}}),e("q-input",{staticStyle:{"margin-bottom":"5px"},attrs:{dense:"",outlined:"",square:"",debounce:"500",type:"number",label:t.$t("stock.view_stocklist.goods_qty")},on:{keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"before",fn:function(){return[e("q-select",{attrs:{dense:"",outlined:"",square:"","use-input":"","hide-selected":"","fill-input":"",label:t.$t("goods.view_goodslist.goods_code"),options:t.options},on:{focus:function(e){return t.getFocus(4)},"input-value":t.setOptions,filter:t.filterFn,keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"no-option",fn:function(){return[e("q-item",[e("q-item-section",{staticClass:"text-grey"},[t._v("No results")])],1)]},proxy:!0},t.goodsData4.code?{key:"append",fn:function(){return[e("q-icon",{staticClass:"cursor-pointer",attrs:{name:"cancel"},on:{click:function(e){e.stopPropagation(),t.goodsData4.code=""}}})]},proxy:!0}:null],null,!0),model:{value:t.goodsData4.code,callback:function(e){t.$set(t.goodsData4,"code",e)},expression:"goodsData4.code"}})]},proxy:!0}]),model:{value:t.goodsData4.qty,callback:function(e){t.$set(t.goodsData4,"qty",t._n(e))},expression:"goodsData4.qty"}}),e("q-input",{staticStyle:{"margin-bottom":"5px"},attrs:{dense:"",outlined:"",square:"",debounce:"500",type:"number",label:t.$t("stock.view_stocklist.goods_qty")},on:{keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"before",fn:function(){return[e("q-select",{attrs:{dense:"",outlined:"",square:"","use-input":"","hide-selected":"","fill-input":"",label:t.$t("goods.view_goodslist.goods_code"),options:t.options},on:{focus:function(e){return t.getFocus(5)},"input-value":t.setOptions,filter:t.filterFn,keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"no-option",fn:function(){return[e("q-item",[e("q-item-section",{staticClass:"text-grey"},[t._v("No results")])],1)]},proxy:!0},t.goodsData5.code?{key:"append",fn:function(){return[e("q-icon",{staticClass:"cursor-pointer",attrs:{name:"cancel"},on:{click:function(e){e.stopPropagation(),t.goodsData5.code=""}}})]},proxy:!0}:null],null,!0),model:{value:t.goodsData5.code,callback:function(e){t.$set(t.goodsData5,"code",e)},expression:"goodsData5.code"}})]},proxy:!0}]),model:{value:t.goodsData5.qty,callback:function(e){t.$set(t.goodsData5,"qty",t._n(e))},expression:"goodsData5.qty"}}),e("q-input",{staticStyle:{"margin-bottom":"5px"},attrs:{dense:"",outlined:"",square:"",debounce:"500",type:"number",label:t.$t("stock.view_stocklist.goods_qty")},on:{keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"before",fn:function(){return[e("q-select",{attrs:{dense:"",outlined:"",square:"","use-input":"","hide-selected":"","fill-input":"",label:t.$t("goods.view_goodslist.goods_code"),options:t.options},on:{focus:function(e){return t.getFocus(6)},"input-value":t.setOptions,filter:t.filterFn,keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"no-option",fn:function(){return[e("q-item",[e("q-item-section",{staticClass:"text-grey"},[t._v("No results")])],1)]},proxy:!0},t.goodsData6.code?{key:"append",fn:function(){return[e("q-icon",{staticClass:"cursor-pointer",attrs:{name:"cancel"},on:{click:function(e){e.stopPropagation(),t.goodsData6.code=""}}})]},proxy:!0}:null],null,!0),model:{value:t.goodsData6.code,callback:function(e){t.$set(t.goodsData6,"code",e)},expression:"goodsData6.code"}})]},proxy:!0}]),model:{value:t.goodsData6.qty,callback:function(e){t.$set(t.goodsData6,"qty",t._n(e))},expression:"goodsData6.qty"}}),e("q-input",{staticStyle:{"margin-bottom":"5px"},attrs:{dense:"",outlined:"",square:"",debounce:"500",type:"number",label:t.$t("stock.view_stocklist.goods_qty")},on:{keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"before",fn:function(){return[e("q-select",{attrs:{dense:"",outlined:"",square:"","use-input":"","hide-selected":"","fill-input":"",label:t.$t("goods.view_goodslist.goods_code"),options:t.options},on:{focus:function(e){return t.getFocus(7)},"input-value":t.setOptions,filter:t.filterFn,keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"no-option",fn:function(){return[e("q-item",[e("q-item-section",{staticClass:"text-grey"},[t._v("No results")])],1)]},proxy:!0},t.goodsData7.code?{key:"append",fn:function(){return[e("q-icon",{staticClass:"cursor-pointer",attrs:{name:"cancel"},on:{click:function(e){e.stopPropagation(),t.goodsData7.code=""}}})]},proxy:!0}:null],null,!0),model:{value:t.goodsData7.code,callback:function(e){t.$set(t.goodsData7,"code",e)},expression:"goodsData7.code"}})]},proxy:!0}]),model:{value:t.goodsData7.qty,callback:function(e){t.$set(t.goodsData7,"qty",t._n(e))},expression:"goodsData7.qty"}}),e("q-input",{staticStyle:{"margin-bottom":"5px"},attrs:{dense:"",outlined:"",square:"",debounce:"500",type:"number",label:t.$t("stock.view_stocklist.goods_qty")},on:{keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"before",fn:function(){return[e("q-select",{attrs:{dense:"",outlined:"",square:"","use-input":"","hide-selected":"","fill-input":"",label:t.$t("goods.view_goodslist.goods_code"),options:t.options},on:{focus:function(e){return t.getFocus(8)},"input-value":t.setOptions,filter:t.filterFn,keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"no-option",fn:function(){return[e("q-item",[e("q-item-section",{staticClass:"text-grey"},[t._v("No results")])],1)]},proxy:!0},t.goodsData8.code?{key:"append",fn:function(){return[e("q-icon",{staticClass:"cursor-pointer",attrs:{name:"cancel"},on:{click:function(e){e.stopPropagation(),t.goodsData8.code=""}}})]},proxy:!0}:null],null,!0),model:{value:t.goodsData8.code,callback:function(e){t.$set(t.goodsData8,"code",e)},expression:"goodsData8.code"}})]},proxy:!0}]),model:{value:t.goodsData8.qty,callback:function(e){t.$set(t.goodsData8,"qty",t._n(e))},expression:"goodsData8.qty"}}),e("q-input",{staticStyle:{"margin-bottom":"5px"},attrs:{dense:"",outlined:"",square:"",debounce:"500",type:"number",label:t.$t("stock.view_stocklist.goods_qty")},on:{keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"before",fn:function(){return[e("q-select",{attrs:{dense:"",outlined:"",square:"","use-input":"","hide-selected":"","fill-input":"",label:t.$t("goods.view_goodslist.goods_code"),options:t.options},on:{focus:function(e){return t.getFocus(9)},"input-value":t.setOptions,filter:t.filterFn,keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"no-option",fn:function(){return[e("q-item",[e("q-item-section",{staticClass:"text-grey"},[t._v("No results")])],1)]},proxy:!0},t.goodsData9.code?{key:"append",fn:function(){return[e("q-icon",{staticClass:"cursor-pointer",attrs:{name:"cancel"},on:{click:function(e){e.stopPropagation(),t.goodsData9.code=""}}})]},proxy:!0}:null],null,!0),model:{value:t.goodsData9.code,callback:function(e){t.$set(t.goodsData9,"code",e)},expression:"goodsData9.code"}})]},proxy:!0}]),model:{value:t.goodsData9.qty,callback:function(e){t.$set(t.goodsData9,"qty",t._n(e))},expression:"goodsData9.qty"}}),e("q-input",{staticStyle:{"margin-bottom":"5px"},attrs:{dense:"",outlined:"",square:"",debounce:"500",type:"number",label:t.$t("stock.view_stocklist.goods_qty")},on:{keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"before",fn:function(){return[e("q-select",{attrs:{dense:"",outlined:"",square:"","use-input":"","hide-selected":"","fill-input":"",label:t.$t("goods.view_goodslist.goods_code"),options:t.options},on:{focus:function(e){return t.getFocus(10)},"input-value":t.setOptions,filter:t.filterFn,keyup:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.isEdit?t.editDataSubmit():t.newDataSubmit()}},scopedSlots:t._u([{key:"no-option",fn:function(){return[e("q-item",[e("q-item-section",{staticClass:"text-grey"},[t._v("No results")])],1)]},proxy:!0},t.goodsData10.code?{key:"append",fn:function(){return[e("q-icon",{staticClass:"cursor-pointer",attrs:{name:"cancel"},on:{click:function(e){e.stopPropagation(),t.goodsData10.code=""}}})]},proxy:!0}:null],null,!0),model:{value:t.goodsData10.code,callback:function(e){t.$set(t.goodsData10,"code",e)},expression:"goodsData10.code"}})]},proxy:!0}]),model:{value:t.goodsData10.qty,callback:function(e){t.$set(t.goodsData10,"qty",t._n(e))},expression:"goodsData10.qty"}})],1),e("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[e("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(e){t.isEdit?t.editDataCancel():t.newDataCancel()}}},[t._v(t._s(t.$t("cancel")))]),e("q-btn",{attrs:{color:"primary"},on:{click:function(e){t.isEdit?t.editDataSubmit():t.newDataSubmit()}}},[t._v(t._s(t.$t("submit")))])],1)],1)],1),e("q-dialog",{model:{value:t.deleteForm,callback:function(e){t.deleteForm=e},expression:"deleteForm"}},[e("q-card",{staticClass:"shadow-24"},[e("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[e("div",[t._v(t._s(t.$t("delete")))]),e("q-space"),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[t._v(t._s(t.$t("index.close")))])],1)],1),e("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[t._v(t._s(t.$t("deletetip")))]),e("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[e("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(e){return t.deleteDataCancel()}}},[t._v(t._s(t.$t("cancel")))]),e("q-btn",{attrs:{color:"primary"},on:{click:function(e){return t.deleteDataSubmit()}}},[t._v(t._s(t.$t("submit")))])],1)],1)],1),e("q-dialog",{model:{value:t.neworderForm,callback:function(e){t.neworderForm=e},expression:"neworderForm"}},[e("q-card",{staticClass:"shadow-24"},[e("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[e("div",[t._v(t._s(t.$t("confirmorder")))]),e("q-space"),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[t._v(t._s(t.$t("index.close")))])],1)],1),e("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[t._v(t._s(t.$t("deletetip")))]),e("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[e("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(e){return t.neworderDataCancel()}}},[t._v(t._s(t.$t("cancel")))]),e("q-btn",{attrs:{color:"primary"},on:{click:function(e){return t.neworderDataSubmit()}}},[t._v(t._s(t.$t("submit")))])],1)],1)],1),e("q-dialog",{model:{value:t.orderreleaseForm,callback:function(e){t.orderreleaseForm=e},expression:"orderreleaseForm"}},[e("q-card",{staticClass:"shadow-24"},[e("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[e("div",[t._v(t._s(t.$t("releaseorder")))]),e("q-space"),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[t._v(t._s(t.$t("index.close")))])],1)],1),e("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[t._v(t._s(t.$t("deletetip")))]),e("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[e("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(e){return t.orderreleaseDataCancel()}}},[t._v(t._s(t.$t("cancel")))]),e("q-btn",{attrs:{color:"primary"},on:{click:function(e){return t.orderreleaseDataSubmit()}}},[t._v(t._s(t.$t("submit")))])],1)],1)],1),e("q-dialog",{model:{value:t.viewForm,callback:function(e){t.viewForm=e},expression:"viewForm"}},[e("q-card",{attrs:{id:"printMe"}},[e("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[e("div",[t._v(t._s(t.viewdn))]),e("q-space"),t._v("\n        "+t._s(t.$t("outbound.dn"))+"\n      ")],1),e("q-card-section",[e("div",{staticClass:"row"},[e("div",{staticClass:"col-8"},[e("div",{staticClass:"text-h6"},[t._v("Sender: "+t._s(t.warehouse_detail.warehouse_name))]),e("div",{staticClass:"text-subtitle2"},[t._v("Address: "+t._s(t.warehouse_detail.warehouse_city)+t._s(t.warehouse_detail.warehouse_address))]),e("div",{staticClass:"text-subtitle2"},[t._v("Tel: "+t._s(t.warehouse_detail.warehouse_contact))]),e("div",{staticClass:"text-h6"},[t._v("Receiver: "+t._s(t.customer_detail.customer_name))]),e("div",{staticClass:"text-subtitle2"},[t._v("Address: "+t._s(t.customer_detail.customer_city)+t._s(t.customer_detail.customer_address))]),e("div",{staticClass:"text-subtitle2"},[t._v("Tel: "+t._s(t.customer_detail.customer_contact))])]),e("div",{staticClass:"col-4"},[e("img",{staticStyle:{width:"70%","margin-left":"15%"},attrs:{src:t.bar_code}})])])]),e("q-markup-table",[e("thead",[e("tr",[e("th",{staticClass:"text-left"},[t._v(t._s(t.$t("goods.view_goodslist.goods_code")))]),e("th",{staticClass:"text-right"},[t._v(t._s(t.$t("outbound.view_dn.total_weight")))]),e("th",{staticClass:"text-right"},[t._v(t._s(t.$t("outbound.view_dn.total_volume")))]),e("th",{staticClass:"text-right"},[t._v(t._s(t.$t("outbound.view_dn.intransit_qty")))]),e("th",{staticClass:"text-right"},[t._v("Comments")])])]),e("tbody",t._l(t.viewprint_table,(function(o,a){return e("tr",{key:a},[e("td",{staticClass:"text-left"},[t._v(t._s(o.goods_code))]),e("td",{staticClass:"text-right"},[t._v(t._s(o.goods_weight))]),e("td",{staticClass:"text-right"},[t._v(t._s(o.goods_volume))]),e("td",{staticClass:"text-right"},[t._v(t._s(o.picked_qty))]),e("td",{staticClass:"text-right"})])})),0)])],1),e("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[e("q-btn",{directives:[{name:"print",rawName:"v-print",value:t.printObj,expression:"printObj"}],attrs:{color:"primary",icon:"print"}},[t._v("print")])],1)],1),e("q-dialog",{model:{value:t.viewPLForm,callback:function(e){t.viewPLForm=e},expression:"viewPLForm"}},[e("q-card",{attrs:{id:"printPL"}},[e("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[e("div",[t._v(t._s(t.$t("print")))]),e("q-space")],1),e("div",{staticClass:"col-4",staticStyle:{"margin-top":"5%"}},[e("img",{staticStyle:{width:"21%","margin-left":"70%"},attrs:{src:t.bar_code}})]),e("q-markup-table",[e("thead",[e("tr",[e("th",{staticClass:"text-left"},[t._v(t._s(t.$t("outbound.view_dn.dn_code")))]),e("th",{staticClass:"text-right"},[t._v(t._s(t.$t("warehouse.view_binset.bin_name")))]),e("th",{staticClass:"text-right"},[t._v(t._s(t.$t("goods.view_goodslist.goods_code")))]),e("th",{staticClass:"text-right"},[t._v(t._s(t.$t("outbound.pickstock")))]),e("th",{staticClass:"text-right"},[t._v(t._s(t.$t("outbound.pickedstock")))]),e("th",{staticClass:"text-right"},[t._v("Comments")])])]),e("tbody",t._l(t.pickinglist_print_table,(function(o,a){return e("tr",{key:a},[e("td",{staticClass:"text-left"},[t._v(t._s(o.dn_code))]),e("td",{staticClass:"text-right"},[t._v(t._s(o.bin_name))]),e("td",{staticClass:"text-right"},[t._v(t._s(o.goods_code))]),e("td",{staticClass:"text-right"},[t._v(t._s(o.pick_qty))]),e("td",{directives:[{name:"show",rawName:"v-show",value:0===t.picklist_check,expression:"picklist_check === 0"}],staticClass:"text-right"}),e("td",{directives:[{name:"show",rawName:"v-show",value:t.picklist_check>0,expression:"picklist_check > 0"}],staticClass:"text-right"},[t._v(t._s(o.picked_qty))]),e("td",{staticClass:"text-right"})])})),0)])],1),e("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[e("q-btn",{directives:[{name:"print",rawName:"v-print",value:t.printPL,expression:"printPL"}],attrs:{color:"primary",icon:"print"}},[t._v("print")])],1)],1),e("q-dialog",{model:{value:t.pickedForm,callback:function(e){t.pickedForm=e},expression:"pickedForm"}},[e("q-card",{staticClass:"shadow-24"},[e("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[e("div",[t._v(t._s(t.pickFormData.dn_code))]),e("q-space"),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[t._v(t._s(t.$t("index.close")))])],1)],1),e("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[e("q-input",{staticStyle:{"margin-bottom":"5px"},attrs:{dense:"",outlined:"",square:"",debounce:"500",disable:"",readonly:"",label:t.$t("baseinfo.view_customer.customer_name")},model:{value:t.pickFormData.customer,callback:function(e){t.$set(t.pickFormData,"customer",e)},expression:"pickFormData.customer"}}),t._l(t.pickFormData.goodsData,(function(o,a){return e("div",{key:a},[e("q-input",{attrs:{dense:"",outlined:"",square:"","bottom-slots":"",type:"number",label:o.goods_code},scopedSlots:t._u([{key:"append",fn:function(){return[t._v("\n              "+t._s(o.bin_name)+"\n            ")]},proxy:!0}],null,!0),model:{value:o.pick_qty,callback:function(e){t.$set(o,"pick_qty",e)},expression:"item.pick_qty"}})],1)}))],2),e("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[e("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(e){return t.pickedDataCancel()}}},[t._v(t._s(t.$t("cancel")))]),e("q-btn",{attrs:{color:"primary"},on:{click:function(e){return t.pickedDataSubmit()}}},[t._v(t._s(t.$t("submit")))])],1)],1)],1),e("q-dialog",{model:{value:t.dispatchForm,callback:function(e){t.dispatchForm=e},expression:"dispatchForm"}},[e("q-card",{staticClass:"shadow-24"},[e("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[e("div",[t._v(t._s(t.dispatchFormData.dn_code))]),e("q-space"),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[t._v(t._s(t.$t("index.close")))])],1)],1),e("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[e("q-select",{attrs:{dense:"",outlined:"",square:"","use-input":"","hide-selected":"","fill-input":"",label:t.$t("driver.view_driver.driver_name"),options:t.driver_options,autofocus:""},on:{filter:t.filterFnDispatch,keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.dispatchDataSubmit()}},scopedSlots:t._u([{key:"no-option",fn:function(){return[e("q-item",[e("q-item-section",{staticClass:"text-grey"},[t._v("No results")])],1)]},proxy:!0},t.dispatchFormData.driver?{key:"append",fn:function(){return[e("q-icon",{staticClass:"cursor-pointer",attrs:{name:"cancel"},on:{click:function(e){e.stopPropagation(),t.dispatchFormData.driver=""}}})]},proxy:!0}:null],null,!0),model:{value:t.dispatchFormData.driver,callback:function(e){t.$set(t.dispatchFormData,"driver",e)},expression:"dispatchFormData.driver"}})],1),e("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[e("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(e){return t.dispatchDataCancel()}}},[t._v(t._s(t.$t("cancel")))]),e("q-btn",{attrs:{color:"primary"},on:{click:function(e){return t.dispatchDataSubmit()}}},[t._v(t._s(t.$t("submit")))])],1)],1)],1),e("q-dialog",{model:{value:t.podForm,callback:function(e){t.podForm=e},expression:"podForm"}},[e("q-card",{staticClass:"shadow-24"},[e("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[e("div",[t._v(t._s(t.$t("outbound.dn"))+": "+t._s(t.podFormData.dn_code))]),e("q-space"),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[t._v(t._s(t.$t("index.close")))])],1)],1),e("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[t._v("\n        "+t._s(t.$t("baseinfo.customer"))+": "+t._s(t.podFormData.customer)+"\n        "),t._l(t.podFormData.goodsData,(function(o,a){return e("div",{key:a},[e("q-input",{staticStyle:{"margin-bottom":"5px"},attrs:{dense:"",outlined:"",square:"",debounce:"500",type:"number",label:t.$t("outbound.view_dn.delivery_actual_qty"),"error-message":t.error2,error:t.isError1,rules:[t.validate1]},scopedSlots:t._u([{key:"after",fn:function(){return[e("q-input",{staticStyle:{"margin-top":"11%"},attrs:{dense:"",outlined:"",square:"",debounce:"500",type:"number",label:t.$t("outbound.view_dn.delivery_damage_qty"),"error-message":t.error2,error:t.isError2,rules:[t.validate2(o.delivery_damage_qty,o.intransit_qty)]},model:{value:o.delivery_damage_qty,callback:function(e){t.$set(o,"delivery_damage_qty",t._n(e))},expression:"item.delivery_damage_qty"}})]},proxy:!0}],null,!0),model:{value:o.intransit_qty,callback:function(e){t.$set(o,"intransit_qty",t._n(e))},expression:"item.intransit_qty"}})],1)}))],2),e("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[e("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(e){return t.PODDataCancel()}}},[t._v(t._s(t.$t("cancel")))]),e("q-btn",{attrs:{color:"primary"},on:{click:function(e){return t.PODDataSubmit()}}},[t._v(t._s(t.$t("submit")))])],1)],1)],1)],2)},s=[],n=o("3004"),i=o("18d6"),r={name:"Pagednlist",data(){return{openid:"",login_name:"",authin:"0",pathname:"dn/",pathname_previous:"",pathname_next:"",separator:"cell",loading:!1,height:"",table_list:[],viewprint_table:[],bar_code:"",pickinglist_print_table:[],pickinglist_check:0,warehouse_detail:{},customer_list:[],customer_list1:[],driver_list:[],customer_detail:{},columns:[{name:"dn_code",required:!0,label:this.$t("outbound.view_dn.dn_code"),align:"left",field:"dn_code"},{name:"dn_status",label:this.$t("outbound.view_dn.dn_status"),field:"dn_status",align:"center"},{name:"total_weight",label:this.$t("outbound.view_dn.total_weight"),field:"total_weight",align:"center"},{name:"total_volume",label:this.$t("outbound.view_dn.total_volume"),field:"total_volume",align:"center"},{name:"customer",label:this.$t("outbound.view_dn.customer"),field:"customer",align:"center"},{name:"creater",label:this.$t("creater"),field:"creater",align:"center"},{name:"create_time",label:this.$t("createtime"),field:"create_time",align:"center"},{name:"update_time",label:this.$t("updatetime"),field:"update_time",align:"center"},{name:"action",label:this.$t("action"),align:"right"}],filter:"",pagination:{page:1,rowsPerPage:"30"},newForm:!1,options1:[],isEdit:!1,listNumber:"",options:i["a"].getItem("goods_code_list"),driver_options:i["a"].getItem("driver_name_list"),newdn:{creater:""},newFormData:{dn_code:"",customer:"",goods_code:[],goods_qty:[],creater:""},pickFormData:{dn_code:"",customer:"",goodsData:[],creater:""},goodsData1:{bin:"",code:"",qty:""},goodsData2:{bin:"",code:"",qty:""},goodsData3:{bin:"",code:"",qty:""},goodsData4:{bin:"",code:"",qty:""},goodsData5:{bin:"",code:"",qty:""},goodsData6:{bin:"",code:"",qty:""},goodsData7:{bin:"",code:"",qty:""},goodsData8:{bin:"",code:"",qty:""},goodsData9:{bin:"",code:"",qty:""},goodsData10:{bin:"",code:"",qty:""},editid:0,editFormData:{},pickedForm:!1,pickedid:0,deleteForm:!1,deleteid:0,neworderForm:!1,neworderid:0,orderreleaseForm:!1,orderreleaseid:0,viewForm:!1,viewPLForm:!1,viewdn:"",viewid:0,dispatchid:0,dispatchForm:!1,dispatchFormData:{dn_code:"",driver:""},podid:0,podForm:!1,podFormData:{dn_code:"",customer:"",goodsData:[]},printObj:{id:"printMe",popTitle:this.$t("outbound.dn")},printPL:{id:"printPL",popTitle:this.$t("outbound.pickinglist")},error1:this.$t("baseinfo.view_customer.error1"),error2:this.$t("notice.valerror"),isError1:!1,isError2:!1,current:1,max:0,total:0,paginationIpt:1}},methods:{validate1(t){const e=/^[1-9]\d*$/g,o=e.test(t);this.isError1=!o},validate2(t,e){const o=/^[0-9]\d*$/g,a=o.test(t);this.isError2=!(a&&t<=e)},getList(){var t=this;i["a"].has("auth")&&Object(n["f"])(t.pathname+"list/?page="+t.current,{}).then((e=>{t.table_list=[],t.total=e.count,0===e.count||1===Math.ceil(e.count/30)?t.max=0:t.max=Math.ceil(e.count/30),e.results.forEach((e=>{1===e.dn_status?e.dn_status=t.$t("outbound.freshorder"):2===e.dn_status?e.dn_status=t.$t("outbound.neworder"):3===e.dn_status?e.dn_status=t.$t("outbound.pickstock"):4===e.dn_status?e.dn_status=t.$t("outbound.pickedstock"):5===e.dn_status?e.dn_status=t.$t("outbound.shippedstock"):6===e.dn_status?e.dn_status=t.$t("outbound.received"):e.dn_status="N/A",t.table_list.push(e)})),e.results.forEach((e=>{1===e.asn_status&&(e.asn_status=t.$t())})),t.customer_list=e.customer_list,t.customer_list1=e.customer_list,t.pathname_previous=e.previous,t.pathname_next=e.next})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},changePageEnter(t){Number(this.paginationIpt)<1?(this.current=1,this.paginationIpt=1):Number(this.paginationIpt)>this.max?(this.current=this.max,this.paginationIpt=this.max):this.current=Number(this.paginationIpt),this.getList()},getSearchList(){var t=this;i["a"].has("auth")&&(t.current=1,t.paginationIpt=1,Object(n["f"])(t.pathname+"list/?dn_code__icontains="+t.filter+"&page="+t.current,{}).then((e=>{t.table_list=[],t.total=e.count,0===e.count||1===Math.ceil(e.count/30)?t.max=0:t.max=Math.ceil(e.count/30),e.results.forEach((e=>{1===e.dn_status?e.dn_status=t.$t("outbound.freshorder"):2===e.dn_status?e.dn_status=t.$t("outbound.neworder"):3===e.dn_status?e.dn_status=t.$t("outbound.pickstock"):4===e.dn_status?e.dn_status=t.$t("outbound.pickedstock"):5===e.dn_status?e.dn_status=t.$t("outbound.shippedstock"):6===e.dn_status?e.dn_status=t.$t("outbound.received"):e.dn_status="N/A",t.table_list.push(e)})),t.customer_list=e.customer_list,t.customer_list1=e.customer_list,t.pathname_previous=e.previous,t.pathname_next=e.next})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})})))},getListPrevious(){var t=this;i["a"].has("auth")&&Object(n["f"])(t.pathname_previous,{}).then((e=>{t.table_list=[],e.results.forEach((e=>{1===e.dn_status?e.dn_status=t.$t("outbound.freshorder"):2===e.dn_status?e.dn_status=t.$t("outbound.neworder"):3===e.dn_status?e.dn_status=t.$t("outbound.pickstock"):4===e.dn_status?e.dn_status=t.$t("outbound.pickedstock"):5===e.dn_status?e.dn_status=t.$t("outbound.shippedstock"):6===e.dn_status?e.dn_status=t.$t("outbound.received"):e.dn_status="N/A",t.table_list.push(e)})),t.customer_list=e.customer_list,t.customer_list1=e.customer_list,t.pathname_previous=e.previous,t.pathname_next=e.next})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},getListNext(){var t=this;i["a"].has("auth")&&Object(n["f"])(t.pathname_next,{}).then((e=>{t.table_list=[],e.results.forEach((e=>{1===e.dn_status?e.dn_status=t.$t("outbound.freshorder"):2===e.dn_status?e.dn_status=t.$t("outbound.neworder"):3===e.dn_status?e.dn_status=t.$t("outbound.pickstock"):4===e.dn_status?e.dn_status=t.$t("outbound.pickedstock"):5===e.dn_status?e.dn_status=t.$t("outbound.shippedstock"):6===e.dn_status?e.dn_status=t.$t("outbound.received"):e.dn_status="N/A",t.table_list.push(e)})),t.customer_list=e.customer_list,t.customer_list1=e.customer_list,t.pathname_previous=e.previous,t.pathname_next=e.next})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},reFresh(){var t=this;t.table_list=[],t.getList()},newFormOpen(){var t=this;t.isEdit=!1,t.goodsDataClear(),t.newForm=!0,t.newdn.creater=t.login_name,Object(n["i"])(t.pathname+"list/",t.newdn).then((e=>{e.detail||(t.newFormData.dn_code=e.dn_code)})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},newDataSubmit(){var t=this;t.newFormData.creater=t.login_name;let e=!1;if(""!==t.newFormData.customer){t.newFormData.goods_code=[],t.newFormData.goods_qty=[];let o=0;for(let a=0;a<10;a++){const s=`goodsData${a+1}`;""!==t[s].code&&""!==t[s].qty&&(t[s].qty<1?(e=!0,t.$q.notify({message:"Total Quantity Must Be > 0",icon:"close",color:"negative"})):(t.newFormData.goods_code.push(t[s].code),t.newFormData.goods_qty.push(t[s].qty)),o+=1)}0===o&&(e=!0,t.$q.notify({message:"Please Enter The Goods & Qty",icon:"close",color:"negative"}))}else e=!0,t.$q.notify({message:"Please Enter The Customer",icon:"close",color:"negative"});e||Object(n["i"])(t.pathname+"detail/",t.newFormData).then((e=>{t.table_list=[],t.getList(),t.newDataCancel(),"success"===e.detail&&t.$q.notify({message:"Success Create",icon:"check",color:"green"})})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},newDataCancel(){var t=this;t.newForm=!1,t.newFormData={dn_code:"",customer:"",goods_code:[],goods_qty:[],creater:""},t.goodsDataClear()},goodsDataClear(){var t=this;for(let e=1;e<=10;e++)t[`goodsData${e}`]={code:"",qty:""}},editData(t){var e=this;e.isEdit=!0,e.goodsDataClear(),t.dn_status!==e.$t("outbound.freshorder")?e.$q.notify({message:t.dn_code+" DN Status Not "+e.$t("outbound.freshorder"),icon:"close",color:"negative"}):(e.newFormData.dn_code=t.dn_code,e.newFormData.customer=t.customer,Object(n["f"])(e.pathname+"detail/?dn_code="+t.dn_code).then((o=>{e.newForm=!0,e.editid=t.id,o.results.forEach(((t,o)=>{e[`goodsData${o+1}`]={code:t.goods_code,qty:t.goods_qty}}))})))},editDataSubmit(){var t=this;t.newFormData.creater=t.login_name;let e=!1;if(""!==t.newFormData.customer){t.newFormData.goods_code=[],t.newFormData.goods_qty=[];let o=0;for(let a=0;a<10;a++){const s=`goodsData${a+1}`;""!==t[s].code&&""!==t[s].qty&&(t[s].qty<1?(e=!0,t.$q.notify({message:"Total Quantity Must Be > 0",icon:"close",color:"negative"})):(t.newFormData.goods_code.push(t[s].code),t.newFormData.goods_qty.push(t[s].qty)),o+=1)}0===o&&(e=!0,t.$q.notify({message:"Please Enter The Goods & Qty",icon:"close",color:"negative"}))}else e=!0,t.$q.notify({message:"Please Enter The Customer",icon:"close",color:"negative"});e||Object(n["j"])(t.pathname+"detail/",t.newFormData).then((e=>{t.table_list=[],t.editDataCancel(),t.getList(),e.detail||t.$q.notify({message:"Success Edit DN",icon:"check",color:"green"})})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},editDataCancel(){var t=this;t.newForm=!1,t.editid=0,t.newFormData={dn_code:"",customer:"",goods_code:[],goods_qty:[],creater:""},t.goodsDataClear()},deleteData(t){var e=this;t.dn_status!==e.$t("outbound.freshorder")?e.$q.notify({message:t.dn_code+" DN Status Is Not "+e.$t("outbound.freshorder"),icon:"close",color:"negative"}):(e.deleteForm=!0,e.deleteid=t.id)},deleteDataSubmit(){var t=this;Object(n["d"])(t.pathname+"list/"+t.deleteid+"/").then((e=>{t.table_list=[],t.deleteDataCancel(),t.getList(),e.detail||t.$q.notify({message:"Success Delete DN",icon:"check",color:"green"})})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},deleteDataCancel(){var t=this;t.deleteForm=!1,t.deleteid=0},neworderData(t){var e=this;t.dn_status!==e.$t("outbound.freshorder")?e.$q.notify({message:t.dn_code+" DN Status Is Not "+e.$t("outbound.freshorder"),icon:"close",color:"negative"}):(e.neworderForm=!0,e.neworderid=t.id)},neworderDataSubmit(){var t=this;Object(n["i"])(t.pathname+"neworder/"+t.neworderid+"/",{}).then((e=>{t.table_list=[],t.neworderDataCancel(),t.getList(),e.detail||t.$q.notify({message:"Success Confirm DN Delivery",icon:"check",color:"green"})})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},neworderDataCancel(){var t=this;t.neworderForm=!1,t.neworderid=0},orderreleaseData(t){var e=this;t.dn_status!==e.$t("outbound.neworder")?e.$q.notify({message:t.dn_code+" DN Status Is Not "+e.$t("outbound.neworder"),icon:"close",color:"negative"}):(e.orderreleaseForm=!0,e.orderreleaseid=t.id)},orderreleaseAllData(){var t=this;Object(n["i"])(t.pathname+"orderrelease/",{}).then((e=>{t.table_list=[],t.getList(),e.detail||t.$q.notify({message:"Success Release All Order",icon:"check",color:"green"})})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},orderreleaseDataSubmit(){var t=this;Object(n["j"])(t.pathname+"orderrelease/"+t.orderreleaseid+"/",{}).then((e=>{t.table_list=[],t.orderreleaseDataCancel(),t.getList(),e.detail||t.$q.notify({message:"Success Release DN Code",icon:"check",color:"green"})})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},orderreleaseDataCancel(){var t=this;t.orderreleaseForm=!1,t.orderreleaseid=0},getFocus(t){this.listNumber=t},setOptions(t){const e=this;t||(e[`goodsData${e.listNumber}`].code="");const o=t.toLowerCase();Object(n["f"])("goods/?goods_code__icontains="+o).then((o=>{const a=[];for(let s=0;s<o.results.length;s++)a.push(o.results[s].goods_code),e.listNumber&&o.results[s].goods_code===t&&(e[`goodsData${e.listNumber}`].code=t);e.options1=a}))},filterFn(t,e,o){t.length<1?o():e((()=>{this.options=this.options1}))},setModel(t){const e=this;e.newFormData.customer=t},filterFnS(t,e,o){var a=this;e((()=>{const e=t.toLocaleLowerCase(),o=a.customer_list1;a.customer_list=o.filter((t=>t.toLocaleLowerCase().indexOf(e)>-1))}))},PrintPickingList(t){var e=this,a=o("d055");a.toDataURL(t.bar_code,[{errorCorrectionLevel:"H",mode:"byte",version:"2",type:"image/jpeg"}]).then((t=>{e.bar_code=t})).catch((t=>{console.error(t)})),e.viewPLForm=!0,Object(n["f"])(e.pathname+"pickinglist/"+t.id+"/").then((t=>{e.pickinglist_print_table=[],e.picklist_check=0,t.forEach((t=>{t.picked_qty>0&&(e.picklist_check+=1)})),e.pickinglist_print_table=t,e.viewPLForm=!0})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},pickedData(t){var e=this;t.dn_status!==e.$t("outbound.pickstock")?e.$q.notify({message:t.dn_code+" DN Status Is Not "+e.$t("outbound.pickstock"),icon:"close",color:"negative"}):(e.pickFormData.dn_code=t.dn_code,e.pickFormData.customer=t.customer,Object(n["f"])(e.pathname+"pickinglist/"+t.id+"/").then((o=>{e.pickedForm=!0,e.pickedid=t.id,e.pickFormData.goodsData=o})))},pickedDataSubmit(){var t=this;t.pickFormData.creater=t.login_name,Object(n["i"])(t.pathname+"picked/"+t.pickedid+"/",t.pickFormData).then((e=>{t.table_list=[],t.pickedDataCancel(),t.getList(),e.detail||t.$q.notify({message:"Success Confirm Picking List",icon:"check",color:"green"})})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},pickedDataCancel(){var t=this;t.pickedForm=!1,t.pickedid=0,t.pickFormData={dn_code:"",customer:"",goodsData:[],creater:""},t.goodsDataClear()},viewData(t){var e=this;Object(n["a"])(e.pathname+"viewprint/"+t.id+"/").then((a=>{e.viewprint_table=a.dn_detail,e.warehouse_detail=a.warehouse_detail,e.customer_detail=a.customer_detail,e.viewdn=t.dn_code;var s=o("d055");s.toDataURL(t.bar_code,[{errorCorrectionLevel:"H",mode:"byte",version:"2",type:"image/jpeg"}]).then((t=>{e.bar_code=t})).catch((t=>{console.error(t)})),e.viewForm=!0}))},filterFnDispatch(t,e,o){var a=this;t.length<1?o():e((()=>{const e=t.toLowerCase();Object(n["f"])("driver/?driver_name__icontains="+e).then((t=>{const e=[];for(let o=0;o<t.results.length;o++)e.push(t.results[o].driver_name);i["a"].set("driver_name_list",e),a.driver_options=i["a"].getItem("driver_name_list"),a.$forceUpdate()})).catch((t=>{a.$q.notify({message:t.detail,icon:"close",color:"negative"})}))}))},DispatchDN(t){var e=this;t.dn_status!==e.$t("outbound.pickedstock")?e.$q.notify({message:t.dn_code+" DN Status Is Not "+e.$t("outbound.pickedstock"),icon:"close",color:"negative"}):(e.dispatchFormData.dn_code=t.dn_code,e.dispatchid=t.id,e.dispatchForm=!0)},dispatchDataCancel(){var t=this;t.dispatchFormData={dn_code:"",driver:""},t.dispatchForm=!1},dispatchDataSubmit(){var t=this;Object(n["i"])(t.pathname+"dispatch/"+t.dispatchid+"/",t.dispatchFormData).then((e=>{t.table_list=[],t.dispatchDataCancel(),t.getList(),e.detail||t.$q.notify({message:"Success Dispatch",icon:"check",color:"green"})})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},PODData(t){var e=this;t.dn_status!==e.$t("outbound.shippedstock")?e.$q.notify({message:t.dn_code+" DN Status Is Not "+e.$t("outbound.shippedstock"),icon:"close",color:"negative"}):(e.podFormData.dn_code=t.dn_code,e.podFormData.customer=t.customer,Object(n["f"])(e.pathname+"detail/?dn_code="+t.dn_code).then((o=>{e.podForm=!0,e.podid=t.id,e.podFormData.goodsData=o.results})))},PODDataCancel(){var t=this;t.podForm=!1,t.podid=0,t.podFormData={dn_code:"",customer:"",goodsData:[]}},PODDataSubmit(){var t=this;t.isError1||t.isError2||Object(n["i"])(t.pathname+"pod/"+t.podid+"/",t.podFormData).then((e=>{t.table_list=[],t.PODDataCancel(),t.getList(),e.detail||t.$q.notify({message:"Success Dispatch",icon:"check",color:"green"})})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))}},created(){var t=this;i["a"].has("openid")?t.openid=i["a"].getItem("openid"):(t.openid="",i["a"].set("openid","")),i["a"].has("login_name")?t.login_name=i["a"].getItem("login_name"):(t.login_name="",i["a"].set("login_name","")),i["a"].has("auth")?(t.authin="1",t.table_list=[],t.getList()):t.authin="0",i["a"].has("goods_code_list")||i["a"].set("goods_code_list",[])},mounted(){var t=this;t.$q.platform.is.electron?t.height=String(t.$q.screen.height-290)+"px":t.height=t.$q.screen.height-290+"px"},updated(){},destroyed(){}},c=r,l=o("42e1"),d=o("8fbe"),u=o("eaac"),p=o("e7a9"),_=o("9c40"),m=o("05c0"),g=o("2c91"),b=o("27f9"),f=o("0016"),h=o("bd08"),y=o("db86"),v=o("1c1c"),k=o("66e5"),q=o("4074"),x=o("0170"),D=o("3b16"),w=o("24e8"),$=o("f09f"),S=o("d847"),C=o("a370"),F=o("ddd8"),E=o("2bb1"),O=o("714f"),N=o("7f67"),P=o("eebe"),L=o.n(P),j=Object(l["a"])(c,a,s,!1,null,null,null);"function"===typeof d["default"]&&Object(d["default"])(j);e["default"]=j.exports;L()(j,"components",{QTable:u["a"],QBtnGroup:p["a"],QBtn:_["a"],QTooltip:m["a"],QSpace:g["a"],QInput:b["a"],QIcon:f["a"],QTr:h["a"],QTd:y["a"],QList:v["a"],QItem:k["a"],QItemSection:q["a"],QItemLabel:x["a"],QPagination:D["a"],QDialog:w["a"],QCard:$["a"],QBar:S["a"],QCardSection:C["a"],QSelect:F["a"],QMarkupTable:E["a"]}),L()(j,"directives",{Ripple:O["a"],ClosePopup:N["a"]})},f248:function(t,e){}}]);