(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[69],{daaf:function(t,a,o){"use strict";o.r(a);var e=function(){var t=this,a=t._self._c;return a("q-page",{staticClass:"flex flex-top"},[[a("div",{staticClass:"q-pa-md"},[a("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[a("q-tabs",[a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"downloadinbound",label:t.$t("inbound.asn"),icon:"img:statics/inbound/asn.png",to:{name:"downloadinbound"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"downloadoutbound",label:t.$t("outbound.dn"),icon:"img:statics/outbound/dnlist.png",to:{name:"downloadoutbound"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"downloadstocklist",label:t.$t("stock.stocklist"),icon:"img:statics/stock/stocklist.png",to:{name:"downloadstocklist"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{directives:[{name:"show",rawName:"v-show",value:"Supplier"!==t.$q.localStorage.getItem("staff_type")&&"Customer"!==t.$q.localStorage.getItem("staff_type"),expression:"$q.localStorage.getItem('staff_type') !== 'Supplier' && $q.localStorage.getItem('staff_type') !== 'Customer'"}],attrs:{name:"downloadbinlist",label:t.$t("stock.stockbinlist"),icon:"img:statics/warehouse/binset.png",to:{name:"downloadbinlist"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"downloadgoodslist",label:t.$t("goods.goods_list"),icon:"img:statics/goods/goodslist.png",to:{name:"downloadgoodslist"},exact:""}})],1)],1)],1)])],a("div",{staticClass:"main-table"},[a("router-view")],1)],2)},s=[],n={name:"download"},i=n,l=o("42e1"),d=o("9989"),c=o("429b"),r=o("7867"),m=o("eebe"),p=o.n(m),u=Object(l["a"])(i,e,s,!1,null,"0abedece",null);a["default"]=u.exports;p()(u,"components",{QPage:d["a"],QTabs:c["a"],QRouteTab:r["a"]})}}]);