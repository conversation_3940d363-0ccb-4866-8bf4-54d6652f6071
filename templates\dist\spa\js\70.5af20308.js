(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[70],{6410:function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t._self._c;return a("q-page",{staticClass:"flex flex-top"},[[a("div",{staticClass:"q-pa-md"},[a("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[a("q-tabs",{model:{value:t.detaillink,callback:function(a){t.detaillink=a},expression:"detaillink"}},[a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"driverlist",label:t.$t("driver.driver"),icon:"img:statics/staff/driver.png",to:{name:"driverlist"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"dispatchlist",label:t.$t("driver.dispatchlist"),icon:"img:statics/outbound/dispatchlist.png",to:{name:"dispatchlist"},exact:""}})],1)],1)],1)])],a("div",{staticClass:"main-table"},[a("router-view")],1)],2)},s=[],n={name:"Pagedriver",data(){return{detaillink:"driverlist"}},methods:{}},l=n,r=e("42e1"),d=e("9989"),c=e("429b"),o=e("7867"),p=e("eebe"),u=e.n(p),m=Object(r["a"])(l,i,s,!1,null,null,null);a["default"]=m.exports;u()(m,"components",{QPage:d["a"],QTabs:c["a"],QRouteTab:o["a"]})}}]);