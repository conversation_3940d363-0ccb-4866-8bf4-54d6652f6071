(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[42],{"03aa":function(t,e){},"159b":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("transition",{attrs:{appear:"","enter-active-class":"animated fadeIn"}},[e("q-table",{staticClass:"my-sticky-header-column-table shadow-24",attrs:{id:"table",data:t.table_list,"row-key":"id",separator:t.separator,loading:t.loading,columns:t.columns,"hide-bottom":"",pagination:t.pagination,"no-data-label":"No data","no-results-label":"No data you want","table-style":{height:t.height},flat:"",bordered:""},on:{"update:pagination":function(e){t.pagination=e}},scopedSlots:t._u([{key:"top",fn:function(){return[e("q-btn-group",{attrs:{push:""}},[e("q-btn",{attrs:{label:t.$t("new"),icon:"add"},on:{click:function(e){t.newForm=!0}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("newtip")))])],1),e("q-btn",{attrs:{label:t.$t("refresh"),icon:"refresh"},on:{click:function(e){return t.reFresh()}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("refreshtip")))])],1),e("q-btn",{attrs:{label:t.$t("download"),icon:"cloud_download"},on:{click:function(e){return t.downloadData()}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("downloadtip")))])],1)],1),e("q-space"),e("q-input",{attrs:{outlined:"",rounded:"",dense:"",debounce:"300",color:"primary",placeholder:t.$t("search")},on:{input:function(e){return t.getSearchList()},keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getSearchList()}},scopedSlots:t._u([{key:"append",fn:function(){return[e("q-icon",{attrs:{name:"search"},on:{click:function(e){return t.getSearchList()}}})]},proxy:!0}]),model:{value:t.filter,callback:function(e){t.filter=e},expression:"filter"}})]},proxy:!0},{key:"body",fn:function(a){return[e("q-tr",{attrs:{props:a}},[a.row.id===t.editid?[e("q-td",{key:"staff_name",attrs:{props:a}},[e("q-input",{attrs:{dense:"",outlined:"",square:"",label:t.$t("staff.view_staff.staff_name"),autofocus:"",rules:[e=>e&&e.length>0||t.error1]},model:{value:t.editFormData.staff_name,callback:function(e){t.$set(t.editFormData,"staff_name",e)},expression:"editFormData.staff_name"}})],1)]:a.row.id!==t.editid?[e("q-td",{key:"staff_name",attrs:{props:a}},[t._v(t._s(a.row.staff_name))])]:t._e(),a.row.id===t.editid?[e("q-td",{key:"staff_type",attrs:{props:a}},[e("q-select",{attrs:{dense:"",outlined:"",square:"",options:t.staff_type_list,"transition-show":"scale","transition-hide":"scale",label:t.$t("staff.view_staff.staff_type"),rules:[e=>e&&e.length>0||t.error2]},model:{value:t.editFormData.staff_type,callback:function(e){t.$set(t.editFormData,"staff_type",e)},expression:"editFormData.staff_type"}})],1)]:a.row.id!==t.editid?[e("q-td",{key:"staff_type",attrs:{props:a}},[t._v(t._s(a.row.staff_type))])]:t._e(),e("q-td",{key:"create_time",attrs:{props:a}},[t._v(t._s(a.row.create_time))]),e("q-td",{key:"update_time",attrs:{props:a}},[t._v(t._s(a.row.update_time))]),t.editMode?t.editMode?[a.row.id===t.editid?[e("q-td",{key:"action",staticStyle:{width:"150px"},attrs:{props:a}},[e("q-btn",{attrs:{round:"",flat:"",push:"",color:"secondary",icon:"check"},on:{click:function(e){return t.editDataSubmit()}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("confirmedit")))])],1),e("q-btn",{attrs:{round:"",flat:"",push:"",color:"red",icon:"close"},on:{click:function(e){return t.editDataCancel()}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("canceledit")))])],1)],1)]:a.row.id!==t.editid?void 0:t._e()]:t._e():[e("q-td",{key:"action",staticStyle:{width:"175px"},attrs:{props:a}},[e("q-btn",{attrs:{round:"",flat:"",push:"",color:"purple",icon:a.row.is_lock?"lock":"lock_open"},on:{click:function(e){return t.unlock(a.row)}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v("\n                    "+t._s(a.row.is_lock?t.$t("staff.view_staff.unlock"):t.$t("staff.view_staff.lock"))+"\n                  ")])],1),e("q-btn",{attrs:{round:"",flat:"",push:"",color:"purple",icon:"edit"},on:{click:function(e){return t.editData(a.row)}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("edit")))])],1),e("q-btn",{attrs:{round:"",flat:"",push:"",color:"dark",icon:"delete"},on:{click:function(e){return t.deleteData(a.row.id)}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("delete")))])],1)],1)]],2)]}}])})],1),[e("div",{directives:[{name:"show",rawName:"v-show",value:0!==t.max,expression:"max !== 0"}],staticClass:"q-pa-lg flex flex-center"},[e("div",[t._v(t._s(t.total)+" ")]),e("q-pagination",{attrs:{color:"black",max:t.max,"max-pages":6,"boundary-links":""},on:{click:function(e){return t.getList()}},model:{value:t.current,callback:function(e){t.current=e},expression:"current"}}),e("div",[e("input",{directives:[{name:"model",rawName:"v-model",value:t.paginationIpt,expression:"paginationIpt"}],staticStyle:{width:"60px","text-align":"center"},domProps:{value:t.paginationIpt},on:{blur:t.changePageEnter,keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.changePageEnter.apply(null,arguments)},input:function(e){e.target.composing||(t.paginationIpt=e.target.value)}}})])],1),e("div",{directives:[{name:"show",rawName:"v-show",value:0===t.max,expression:"max === 0"}],staticClass:"q-pa-lg flex flex-center"},[e("q-btn",{attrs:{flat:"",push:"",color:"dark",label:t.$t("no_data")}})],1)],e("q-dialog",{model:{value:t.newForm,callback:function(e){t.newForm=e},expression:"newForm"}},[e("q-card",{staticClass:"shadow-24"},[e("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[e("div",[t._v(t._s(t.$t("newtip")))]),e("q-space"),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[t._v(t._s(t.$t("index.close")))])],1)],1),e("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[e("q-input",{attrs:{dense:"",outlined:"",square:"",label:t.$t("staff.view_staff.staff_name"),autofocus:"",rules:[e=>e&&e.length>0||t.error1]},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.newDataSubmit()}},model:{value:t.newFormData.staff_name,callback:function(e){t.$set(t.newFormData,"staff_name","string"===typeof e?e.trim():e)},expression:"newFormData.staff_name"}}),e("q-select",{staticStyle:{"margin-top":"5px"},attrs:{dense:"",outlined:"",square:"",options:t.staff_type_list,"transition-show":"scale","transition-hide":"scale",label:t.$t("staff.view_staff.staff_type"),rules:[e=>e&&e.length>0||t.error2]},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.newDataSubmit()}},model:{value:t.newFormData.staff_type,callback:function(e){t.$set(t.newFormData,"staff_type",e)},expression:"newFormData.staff_type"}})],1),e("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[e("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(e){return t.newDataCancel()}}},[t._v(t._s(t.$t("cancel")))]),e("q-btn",{attrs:{color:"primary"},on:{click:function(e){return t.newDataSubmit()}}},[t._v(t._s(t.$t("submit")))])],1)],1)],1),e("q-dialog",{model:{value:t.deleteForm,callback:function(e){t.deleteForm=e},expression:"deleteForm"}},[e("q-card",{staticClass:"shadow-24"},[e("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[e("div",[t._v(t._s(t.$t("delete")))]),e("q-space"),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[t._v(t._s(t.$t("index.close")))])],1)],1),e("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[t._v(t._s(t.$t("deletetip")))]),e("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[e("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(e){return t.deleteDataCancel()}}},[t._v(t._s(t.$t("cancel")))]),e("q-btn",{attrs:{color:"primary"},on:{click:function(e){return t.deleteDataSubmit()}}},[t._v(t._s(t.$t("submit")))])],1)],1)],1)],2)},s=[],o=(a("5319"),a("3004")),i=a("bd4c"),r=a("a357"),f=a("18d6"),l={name:"Pagestafflist",data(){return{openid:"",login_name:"",authin:"0",pathname:"staff/",pathname_previous:"",pathname_next:"",separator:"cell",loading:!1,height:"",table_list:[],staff_type_list:[],columns:[{name:"staff_name",required:!0,label:this.$t("staff.view_staff.staff_name"),align:"left",field:"staff_name"},{name:"staff_type",label:this.$t("staff.view_staff.staff_type"),field:"staff_type",align:"center"},{name:"create_time",label:this.$t("createtime"),field:"create_time",align:"center"},{name:"update_time",label:this.$t("updatetime"),field:"update_time",align:"center"},{name:"action",label:this.$t("action"),align:"right"}],pagination:{page:1,rowsPerPage:"30"},newForm:!1,newFormData:{staff_name:"",staff_type:"",check_code:""},editid:0,editFormData:{},editMode:!1,deleteForm:!1,deleteid:0,filter:"",error1:this.$t("staff.view_staff.error1"),error2:this.$t("staff.view_staff.error2"),current:1,max:0,total:0,paginationIpt:1}},methods:{getList(){var t=this;Object(o["f"])(t.pathname+"?page="+t.current,{}).then((e=>{t.table_list=e.results,t.total=e.count,0===e.count||1===Math.ceil(e.count/30)?t.max=0:t.max=Math.ceil(e.count/30),"zh-hans"===f["a"].getItem("lang")&&t.table_list.forEach(((t,e)=>{"Admin"===t.staff_type?t.staff_type="管理员":"Customer"===t.staff_type?t.staff_type="客户":"Supplier"===t.staff_type?t.staff_type="供应商":"Manager"===t.staff_type?t.staff_type="经理":"Supervisor"===t.staff_type?t.staff_type="主管":"Inbound"===t.staff_type?t.staff_type="收货组":"Outbound"===t.staff_type?t.staff_type="发货组":"StockControl"===t.staff_type&&(t.staff_type="库存管理")})),t.pathname_previous=e.previous,t.pathname_next=e.next})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},changePageEnter(t){Number(this.paginationIpt)<1?(this.current=1,this.paginationIpt=1):Number(this.paginationIpt)>this.max?(this.current=this.max,this.paginationIpt=this.max):this.current=Number(this.paginationIpt),this.getList()},getSearchList(){var t=this;t.filter=t.filter.replace(/\s+/g,""),t.current=1,t.paginationIpt=1,Object(o["f"])(t.pathname+"?staff_name__icontains="+t.filter+"&page="+t.current,{}).then((e=>{t.table_list=e.results,t.total=e.count,0===e.count||1===Math.ceil(e.count/30)?t.max=0:t.max=Math.ceil(e.count/30),"zh-hans"===f["a"].getItem("lang")&&t.table_list.forEach(((t,e)=>{"Admin"===t.staff_type?t.staff_type="管理员":"Customer"===t.staff_type?t.staff_type="客户":"Supplier"===t.staff_type?t.staff_type="供应商":"Manager"===t.staff_type?t.staff_type="经理":"Supervisor"===t.staff_type?t.staff_type="主管":"Inbound"===t.staff_type?t.staff_type="收货组":"Outbound"===t.staff_type?t.staff_type="发货组":"StockControl"===t.staff_type&&(t.staff_type="库存管理")})),t.pathname_previous=e.previous,t.pathname_next=e.next})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},getListPrevious(){var t=this;Object(o["f"])(t.pathname_previous,{}).then((e=>{t.table_list=e.results,"zh-hans"===f["a"].getItem("lang")&&t.table_list.forEach(((t,e)=>{"Admin"===t.staff_type?t.staff_type="管理员":"Customer"===t.staff_type?t.staff_type="客户":"Supplier"===t.staff_type?t.staff_type="供应商":"Manager"===t.staff_type?t.staff_type="经理":"Supervisor"===t.staff_type?t.staff_type="主管":"Inbound"===t.staff_type?t.staff_type="收货组":"Outbound"===t.staff_type?t.staff_type="发货组":"StockControl"===t.staff_type&&(t.staff_type="库存管理")})),t.pathname_previous=e.previous,t.pathname_next=e.next})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},getListNext(){var t=this;Object(o["f"])(t.pathname_next,{}).then((e=>{t.table_list=e.results,"zh-hans"===f["a"].getItem("lang")&&t.table_list.forEach(((t,e)=>{"Admin"===t.staff_type?t.staff_type="管理员":"Customer"===t.staff_type?t.staff_type="客户":"Supplier"===t.staff_type?t.staff_type="供应商":"Manager"===t.staff_type?t.staff_type="经理":"Supervisor"===t.staff_type?t.staff_type="主管":"Inbound"===t.staff_type?t.staff_type="收货组":"Outbound"===t.staff_type?t.staff_type="发货组":"StockControl"===t.staff_type&&(t.staff_type="库存管理")})),t.pathname_previous=e.previous,t.pathname_next=e.next})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},reFresh(){var t=this;t.getList()},unlock(t){Object(o["j"])(this.pathname+t.id+"/",{is_lock:!t.is_lock,staff_name:t.staff_name,staff_type:t.staff_type}).then((e=>{this.getList();let a="Success unlocked";t.is_lock||(a="Success locked"),this.$q.notify({message:a,icon:"check",color:"green"})})).catch((t=>{this.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},RandomCheckCode(){for(var t=this,e="",a=4,n=[0,1,2,3,4,5,6,7,8,9],s=0;s<a;s++){var o=Math.floor(9*Math.random());e+=n[o]}t.newFormData.check_code=e},newDataSubmit(){var t=this,e=[];t.newFormData.is_lock=!1,t.table_list.forEach((t=>{e.push(t.staff_name)})),-1===e.indexOf(t.newFormData.staff_name)&&0!==t.newFormData.staff_name.length&&t.newFormData.staff_type?(t.RandomCheckCode(),"经理"===t.newFormData.staff_type?t.newFormData.staff_type="Manager":"主管"===t.newFormData.staff_type?t.newFormData.staff_type="Supervisor":"收货组"===t.newFormData.staff_type?t.newFormData.staff_type="Inbound":"发货组"===t.newFormData.staff_type?t.newFormData.staff_type="Outbound":"库存控制"===t.newFormData.staff_type?t.newFormData.staff_type="StockControl":"客户"===t.newFormData.staff_type?t.newFormData.staff_type="Customer":"供应商"===t.newFormData.staff_type&&(t.newFormData.staff_type="Supplier"),Object(o["i"])(t.pathname,t.newFormData).then((e=>{t.getList(),t.newDataCancel(),t.$q.notify({message:"Success Create",icon:"check",color:"green"})})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))):-1!==e.indexOf(t.newFormData.staff_name)?t.$q.notify({message:t.$t("notice.userererror"),icon:"close",color:"negative"}):0===t.newFormData.staff_name.length?t.$q.notify({message:t.$t("staff.view_staff.error1"),icon:"close",color:"negative"}):t.newFormData.staff_type||t.$q.notify({message:t.$t("staff.view_staff.error2"),icon:"close",color:"negative"})},newDataCancel(){var t=this;t.newForm=!1,t.newFormData={staff_name:"",staff_type:""}},editData(t){var e=this;e.editMode=!0,e.editid=t.id,e.editFormData={staff_name:t.staff_name,staff_type:t.staff_type}},editDataSubmit(){var t=this;"经理"===t.editFormData.staff_type?t.editFormData.staff_type="Manager":"主管"===t.editFormData.staff_type?t.editFormData.staff_type="Supervisor":"收货组"===t.editFormData.staff_type?t.editFormData.staff_type="Inbound":"发货组"===t.editFormData.staff_type?t.editFormData.staff_type="Outbound":"库存控制"===t.editFormData.staff_type?t.editFormData.staff_type="StockControl":"客户"===t.editFormData.staff_type?t.editFormData.staff_type="Customer":"供应商"===t.editFormData.staff_type&&(t.editFormData.staff_type="Supplier"),Object(o["j"])(t.pathname+t.editid+"/",t.editFormData).then((e=>{t.editDataCancel(),t.getList(),t.$q.notify({message:"Success Edit Data",icon:"check",color:"green"})})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},editDataCancel(){var t=this;t.editMode=!1,t.editid=0,t.editFormData={staff_name:"",staff_type:""}},deleteData(t){var e=this;e.deleteForm=!0,e.deleteid=t},deleteDataSubmit(){var t=this;Object(o["d"])(t.pathname+t.deleteid+"/").then((e=>{t.deleteDataCancel(),t.getList(),t.$q.notify({message:"Success Edit Data",icon:"check",color:"green"})})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},deleteDataCancel(){var t=this;t.deleteForm=!1,t.deleteid=0},downloadData(){var t=this;f["a"].has("auth")?Object(o["g"])(t.pathname+"file/?lang="+f["a"].getItem("lang")).then((e=>{var a=Date.now(),n=i["b"].formatDate(a,"YYYYMMDDHHmmssSSS");const s=Object(r["a"])(t.pathname+n+".csv","\ufeff"+e.data,"text/csv");!0!==s&&this.$q.notify({message:"Browser denied file download...",color:"negative",icon:"warning"})})):t.$q.notify({message:t.$t("notice.loginerror"),color:"negative",icon:"warning"})}},created(){var t=this;f["a"].has("openid")?t.openid=f["a"].getItem("openid"):(t.openid="",f["a"].set("openid","")),f["a"].has("login_name")?t.login_name=f["a"].getItem("login_name"):(t.login_name="",f["a"].set("login_name","")),f["a"].has("auth")?(t.authin="1",t.getList()):t.authin="0"},mounted(){var t=this;t.$q.platform.is.electron?t.height=String(t.$q.screen.height-290)+"px":t.height=t.$q.screen.height-290+"px","zh-hans"===f["a"].getItem("lang")?t.staff_type_list=["经理","主管","收货组","发货组","库存控制","客户","供应商"]:t.staff_type_list=["Manager","Supervisor","Inbount","Outbound","StockControl","Customer","Supplier"]},updated(){},destroyed(){}},c=l,p=a("42e1"),d=a("5ab8"),_=a("eaac"),u=a("e7a9"),m=a("9c40"),h=a("05c0"),y=a("2c91"),g=a("27f9"),b=a("0016"),w=a("bd08"),v=a("db86"),k=a("ddd8"),x=a("3b16"),D=a("24e8"),q=a("f09f"),F=a("d847"),$=a("a370"),S=a("7f67"),C=a("eebe"),I=a.n(C),O=Object(p["a"])(c,n,s,!1,null,null,null);"function"===typeof d["default"]&&Object(d["default"])(O);e["default"]=O.exports;I()(O,"components",{QTable:_["a"],QBtnGroup:u["a"],QBtn:m["a"],QTooltip:h["a"],QSpace:y["a"],QInput:g["a"],QIcon:b["a"],QTr:w["a"],QTd:v["a"],QSelect:k["a"],QPagination:x["a"],QDialog:D["a"],QCard:q["a"],QBar:F["a"],QCardSection:$["a"]}),I()(O,"directives",{ClosePopup:S["a"]})},"5ab8":function(t,e,a){"use strict";var n=a("03aa"),s=a.n(n);e["default"]=s.a}}]);