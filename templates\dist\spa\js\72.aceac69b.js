(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[72],{e4df:function(t,a,o){"use strict";o.r(a);var s=function(){var t=this,a=t._self._c;return a("q-page",{staticClass:"flex flex-top"},[[a("div",{staticClass:"q-pa-md"},[a("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[a("q-tabs",{model:{value:t.detaillink,callback:function(a){t.detaillink=a},expression:"detaillink"}},[a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"goodslist",label:t.$t("goods.goods_list"),icon:"img:statics/goods/goodslist.png",to:{name:"goodslist"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"goodsunit",label:t.$t("goods.unit"),icon:"img:statics/goods/goodsunit.png",to:{name:"goodsunit"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"goodsclass",label:t.$t("goods.class"),icon:"img:statics/goods/goodsclass.png",to:{name:"goodsclass"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"goodscolor",label:t.$t("goods.color"),icon:"img:statics/goods/goodscolor.png",to:{name:"goodscolor"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"goodsbrand",label:t.$t("goods.brand"),icon:"img:statics/goods/goodsbrand.png",to:{name:"goodsbrand"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"goodsshape",label:t.$t("goods.shape"),icon:"img:statics/goods/goodsshape.png",to:{name:"goodsshape"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"goodsspecs",label:t.$t("goods.specs"),icon:"img:statics/goods/goodsspecs.png",to:{name:"goodsspecs"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"goodsorigin",label:t.$t("goods.origin"),icon:"img:statics/goods/goodsorigin.png",to:{name:"goodsorigin"},exact:""}})],1)],1)],1)])],a("div",{staticClass:"main-table"},[a("router-view")],1)],2)},e=[],n={name:"Pagegoods",data(){return{detaillink:"goodslist"}},methods:{}},i=n,r=o("42e1"),d=o("9989"),c=o("429b"),g=o("7867"),l=o("eebe"),m=o.n(l),p=Object(r["a"])(i,s,e,!1,null,null,null);a["default"]=p.exports;m()(p,"components",{QPage:d["a"],QTabs:c["a"],QRouteTab:g["a"]})}}]);