(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[48],{4565:function(t,e){},"51a2":function(t,e,o){"use strict";o.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("transition",{attrs:{appear:"","enter-active-class":"animated fadeIn"}},[e("q-table",{staticClass:"my-sticky-header-column-table shadow-24",attrs:{data:t.table_list,"row-key":"id",separator:t.separator,loading:t.loading,columns:t.columns,"hide-bottom":"",pagination:t.pagination,"no-data-label":"No data","no-results-label":"No data you want","table-style":{height:t.height},flat:"",bordered:""},on:{"update:pagination":function(e){t.pagination=e}},scopedSlots:t._u([{key:"top",fn:function(){return[e("q-btn-group",{attrs:{push:""}},[e("q-btn",{attrs:{label:t.$t("handcount.handcount"),icon:"refresh"},on:{click:function(e){t.handcountVisible=!0}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("handcount.handcount")))])],1),e("q-btn",{attrs:{label:t.$t("stock.view_stocklist.downloadcyclecount"),icon:"cloud_download"},on:{click:function(e){return t.downloadData()}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v("\n              "+t._s(t.$t("stock.view_stocklist.downloadcyclecounttip"))+"\n            ")])],1)],1),e("q-space"),e("q-btn-group",{attrs:{push:""}},[e("q-btn",{attrs:{color:"purple",label:t.$t("stock.view_stocklist.cyclecountresult")},on:{click:function(e){return t.ConfirmCounts()}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v("\n              "+t._s(t.$t("stock.view_stocklist.cyclecountresulttip"))+"\n            ")])],1)],1)]},proxy:!0},{key:"body",fn:function(o){return[e("q-tr",{attrs:{props:o}},[e("q-td",{key:"bin_name",attrs:{props:o}},[t._v(t._s(o.row.bin_name))]),e("q-td",{key:"goods_code",attrs:{props:o}},[t._v(t._s(o.row.goods_code))]),e("q-td",{key:"goods_qty",attrs:{props:o}},[t._v(t._s(o.row.goods_qty))]),e("q-td",{key:"physical_inventory",attrs:{props:o}},[e("q-input",{attrs:{dense:"",outlined:"",square:"",type:"number",label:t.$t("stock.view_stocklist.physical_inventory"),rules:[e=>e&&e>0||0==e||t.error1]},on:{blur:function(e){return t.blurHandler(o.row.physical_inventory)}},model:{value:o.row.physical_inventory,callback:function(e){t.$set(o.row,"physical_inventory",t._n(e))},expression:"props.row.physical_inventory"}})],1),e("q-td",{key:"difference",attrs:{props:o}},[t._v(t._s(o.row.physical_inventory-o.row.goods_qty))]),e("q-td",{key:"action",staticStyle:{width:"50px"},attrs:{props:o}},[e("q-btn",{attrs:{round:"",flat:"",push:"",color:"purple",icon:"repeat"},on:{click:function(t){o.row.physical_inventory=0}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v("\n                "+t._s(t.$t("stock.view_stocklist.recyclecounttip"))+"\n              ")])],1)],1)],1)]}}])})],1),[e("div",{staticClass:"q-pa-lg flex flex-center"},[e("q-btn",{attrs:{flat:"",push:"",color:"dark",label:t.$t("no_data")}})],1)],e("q-dialog",{model:{value:t.CountFrom,callback:function(e){t.CountFrom=e},expression:"CountFrom"}},[e("q-card",{staticClass:"shadow-24"},[e("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[e("div",[t._v(t._s(t.$t("confirminventoryresults")))]),e("q-space"),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[t._v(t._s(t.$t("index.close")))])],1)],1),e("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[t._v(t._s(t.$t("deletetip")))]),e("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[e("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(e){return t.preloadDataCancel()}}},[t._v(t._s(t.$t("cancel")))]),e("q-btn",{attrs:{color:"primary"},on:{click:function(e){return t.ConfirmCount()}}},[t._v(t._s(t.$t("submit")))])],1)],1)],1),e("q-dialog",{model:{value:t.handcountVisible,callback:function(e){t.handcountVisible=e},expression:"handcountVisible"}},[e("q-card",{staticClass:"shadow-24"},[e("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[e("div",[t._v(t._s(t.$t("handcount.handcount")))]),e("q-space"),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[t._v(t._s(t.$t("index.close")))])],1)],1),e("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[e("q-select",{ref:"one",attrs:{dense:"",outlined:"",square:"","use-input":"","hide-selected":"","fill-input":"",label:t.$t("goods.view_goodslist.goods_code"),options:t.skuOptions},on:{"input-value":t.setOptions,filter:t.filterFn},scopedSlots:t._u([{key:"no-option",fn:function(){return[e("q-item",[e("q-item-section",{staticClass:"text-grey"},[t._v("No results")])],1)]},proxy:!0},t.handcountVal?{key:"append",fn:function(){return[e("q-icon",{staticClass:"cursor-pointer",attrs:{name:"cancel"},on:{click:function(e){e.stopPropagation(),t.handcountVal=""}}})]},proxy:!0}:null],null,!0),model:{value:t.handcountVal,callback:function(e){t.handcountVal=e},expression:"handcountVal"}}),e("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[e("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(e){t.handcountVisible=!1,t.handcountVal=""}}},[t._v(t._s(t.$t("cancel")))]),e("q-btn",{attrs:{color:"primary"},on:{click:t.handleHandcountSubmit}},[t._v(t._s(t.$t("submit")))])],1)],1)],1)],1)],2)},a=[],s=(o("5319"),o("bd4c")),i=o("a357"),c=o("18d6"),l=o("a639"),r=o("3004"),d={name:"cyclyecount",data(){return{openid:"",login_name:"",authin:"0",pathname:"cyclecount/getgoodscyclecount/",separator:"cell",loading:!1,height:"",table_list:[],bin_size_list:[],bin_property_list:[],warehouse_list:[],columns:[{name:"bin_name",required:!0,label:this.$t("warehouse.view_binset.bin_name"),align:"left",field:"bin_name"},{name:"goods_code",label:this.$t("stock.view_stocklist.goods_code"),field:"goods_code",align:"center"},{name:"goods_qty",label:this.$t("stock.view_stocklist.on_hand_inventory"),field:"goods_qty",align:"center"},{name:"physical_inventory",label:this.$t("stock.view_stocklist.physical_inventory"),field:"physical_inventory",align:"center"},{name:"difference",label:this.$t("stock.view_stocklist.difference"),field:"difference",align:"center"},{name:"action",label:this.$t("action"),align:"right"}],pagination:{page:1,rowsPerPage:"10000"},options:[],error1:this.$t("stock.view_stocklist.error1"),CountFrom:!1,handcountVisible:!1,handcountVal:"",skuOptions:l["a"].getItem("goods_code"),options1:[]}},methods:{setOptions(t){const e=this,o=t.toLowerCase();Object(r["f"])("goods/?goods_code__icontains="+o).then((t=>{const o=[];for(let e=0;e<t.results.length;e++)o.push(t.results[e].goods_code);e.options1=o}))},filterFn(t,e,o){t.length<1?o():e((()=>{this.skuOptions=this.options1}))},getList(){var t=this;Object(r["f"])("cyclecount/manualcyclecount/").then((e=>{t.table_list=e,t.handcountVisible=!1})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},reFresh(){var t=this;t.getList()},ConfirmCount(){var t=this;t.table_list.length?Object(r["i"])("cyclecount/manualcyclecount/",t.table_list).then((e=>{t.CountFrom=!1,t.$q.notify({message:"Success Confirm Cycle Count",icon:"check",color:"green"}),t.table_list=[],t.reFresh()})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})})):(t.CountFrom=!1,t.$q.notify({message:t.$t("notice.cyclecounterror"),icon:"close",color:"negative"}))},preloadDataCancel(){var t=this;t.CountFrom=!1},downloadData(){var t=this;c["a"].has("auth")?Object(r["g"])("cyclecount/manualfilecyclecount/?lang="+c["a"].getItem("lang")).then((e=>{var o=Date.now(),n=s["b"].formatDate(o,"YYYYMMDDHHmmssSSS");const a=Object(i["a"])("manualcyclecountday_"+n+".csv","\ufeff"+e.data,"text/csv");!0!==a&&t.$q.notify({message:"Browser denied file download...",color:"negative",icon:"warning"})})):t.$q.notify({message:t.$t("notice.loginerror"),color:"negative",icon:"warning"})},ConfirmCounts(){var t=this;t.CountFrom=!0},blurHandler(t){t=t.toString().replace(/^(0+)|[^\d]+/g,"")},handleHandcountSubmit(){if(this.handcountVal){var t=this;Object(r["f"])(`cyclecount/getgoodscyclecount/?goods_code=${this.handcountVal}`).then((t=>{this.getList(),this.handcountVal=""})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))}else this.$q.notify({message:"Please Enter SKU",icon:"close",color:"negative"})}},created(){var t=this;c["a"].has("openid")?t.openid=c["a"].getItem("openid"):(t.openid="",c["a"].set("openid","")),c["a"].has("login_name")?t.login_name=c["a"].getItem("login_name"):(t.login_name="",c["a"].set("login_name","")),c["a"].has("auth")?(t.authin="1",t.getList()):t.authin="0"},mounted(){var t=this;t.$q.platform.is.electron?t.height=String(t.$q.screen.height-290)+"px":t.height=t.$q.screen.height-290+"px"},updated(){},destroyed(){}},u=d,p=o("42e1"),h=o("9710"),g=o("eaac"),b=o("e7a9"),_=o("9c40"),f=o("05c0"),y=o("2c91"),m=o("bd08"),v=o("db86"),q=o("27f9"),w=o("24e8"),k=o("f09f"),x=o("d847"),$=o("a370"),C=o("ddd8"),S=o("66e5"),V=o("4074"),Q=o("0016"),O=o("7f67"),F=o("eebe"),D=o.n(F),I=Object(p["a"])(u,n,a,!1,null,null,null);"function"===typeof h["default"]&&Object(h["default"])(I);e["default"]=I.exports;D()(I,"components",{QTable:g["a"],QBtnGroup:b["a"],QBtn:_["a"],QTooltip:f["a"],QSpace:y["a"],QTr:m["a"],QTd:v["a"],QInput:q["a"],QDialog:w["a"],QCard:k["a"],QBar:x["a"],QCardSection:$["a"],QSelect:C["a"],QItem:S["a"],QItemSection:V["a"],QIcon:Q["a"]}),D()(I,"directives",{ClosePopup:O["a"]})},9710:function(t,e,o){"use strict";var n=o("4565"),a=o.n(n);e["default"]=a.a}}]);