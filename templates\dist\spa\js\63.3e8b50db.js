(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[63],{ee34:function(a,t,e){"use strict";e.r(t);var n=function(){var a=this,t=a._self._c;return t("q-page",{staticClass:"flex flex-top"},[[t("div",{staticClass:"q-pa-md"},[t("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[t("q-tabs",{model:{value:a.detaillink,callback:function(t){a.detaillink=t},expression:"detaillink"}},[t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"company",label:a.$t("baseinfo.company_info"),icon:"img:statics/baseinfo/company.png",to:{name:"company"},exact:""}})],1),t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"supplier",label:a.$t("baseinfo.supplier"),icon:"img:statics/baseinfo/supplier.png",to:{name:"supplier"},exact:""}})],1),t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"customer",label:a.$t("baseinfo.customer"),icon:"img:statics/baseinfo/customer.png",to:{name:"customer"},exact:""}})],1)],1)],1)])],t("div",{staticClass:"main-table"},[t("router-view")],1)],2)},s=[],i={name:"Pagebaseinfo",data(){return{detaillink:"company"}},methods:{}},o=i,l=e("42e1"),c=e("9989"),r=e("429b"),p=e("7867"),m=e("eebe"),u=e.n(m),b=Object(l["a"])(o,n,s,!1,null,null,null);t["default"]=b.exports;u()(b,"components",{QPage:c["a"],QTabs:r["a"],QRouteTab:p["a"]})}}]);