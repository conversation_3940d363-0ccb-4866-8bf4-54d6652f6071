(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[67],{c2e4:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{"margin-top":"42px"}},[e("transition",{attrs:{appear:"","enter-active-class":"animated fadeIn"}},[e("q-table",{staticClass:"shadow-24",attrs:{data:t.table_list,"row-key":"id",separator:t.separator,loading:t.loading,columns:t.columns,"hide-bottom":"",pagination:t.pagination,"no-data-label":"No data","no-results-label":"No data you want","table-style":{height:t.height},flat:"",bordered:""},on:{"update:pagination":function(e){t.pagination=e}},scopedSlots:t._u([{key:"top",fn:function(){return[e("q-btn-group",{attrs:{push:""}},[e("q-btn",{attrs:{label:t.$t("refresh"),icon:"refresh"},on:{click:function(e){return t.reFresh()}}},[e("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[t._v(t._s(t.$t("refreshtip")))])],1)],1),e("q-space"),e("q-input",{attrs:{outlined:"",rounded:"",dense:"",debounce:"300",color:"primary",placeholder:t.$t("search")},on:{input:function(e){return t.getSearchList()},keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getSearchList()}},scopedSlots:t._u([{key:"append",fn:function(){return[e("q-icon",{attrs:{name:"search"},on:{click:function(e){return t.getSearchList()}}})]},proxy:!0}]),model:{value:t.filter,callback:function(e){t.filter=e},expression:"filter"}})]},proxy:!0},{key:"body",fn:function(a){return[e("q-tr",{attrs:{props:a}},[e("q-td",{key:"goods_code",attrs:{props:a}},[t._v(t._s(a.row.goods_code))]),e("q-td",{key:"bin_name",attrs:{props:a}},[t._v(t._s(a.row.bin_name))]),e("q-td",{key:"goods_desc",attrs:{props:a}},[t._v(t._s(a.row.goods_desc))]),e("q-td",{key:"mode_code",attrs:{props:a}},[t._v(t._s(a.row.mode_code))]),e("q-td",{key:"goods_qty",attrs:{props:a}},[t._v(t._s(a.row.goods_qty))]),e("q-td",{key:"creater",attrs:{props:a}},[t._v(t._s(a.row.creater))]),e("q-td",{key:"create_time",attrs:{props:a}},[t._v(t._s(a.row.create_time))]),e("q-td",{key:"update_time",attrs:{props:a}},[t._v(t._s(a.row.update_time))])],1)]}}])})],1),[e("div",{directives:[{name:"show",rawName:"v-show",value:0!==t.max,expression:"max !== 0"}],staticClass:"q-pa-lg flex flex-center"},[e("div",[t._v(t._s(t.total)+" ")]),e("q-pagination",{attrs:{color:"black",max:t.max,"max-pages":6,"boundary-links":""},on:{click:function(e){return t.getList()}},model:{value:t.current,callback:function(e){t.current=e},expression:"current"}}),e("div",[e("input",{directives:[{name:"model",rawName:"v-model",value:t.paginationIpt,expression:"paginationIpt"}],staticStyle:{width:"60px","text-align":"center"},domProps:{value:t.paginationIpt},on:{blur:t.changePageEnter,keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.changePageEnter.apply(null,arguments)},input:function(e){e.target.composing||(t.paginationIpt=e.target.value)}}})])],1),e("div",{directives:[{name:"show",rawName:"v-show",value:0===t.max,expression:"max === 0"}],staticClass:"q-pa-lg flex flex-center"},[e("q-btn",{attrs:{flat:"",push:"",color:"dark",label:t.$t("no_data")}})],1)]],2)},i=[],o=a("3004"),s=a("18d6"),r={name:"PageInbAndOutb",data(){return{login_name:"",authin:"0",pathname:"dashboard/",pathname_previous:"",pathname_next:"",separator:"cell",loading:!1,height:"",table_list:[],filter:"",staff_type_list:["Manager","Inbound","Outbound","Supervisor","StockControl","Customer","Supplier"],columns:[{name:"goods_code",label:this.$t("dashboards.view_tradelist.goods_code"),field:"goods_code",align:"left"},{name:"bin_name",label:this.$t("dashboards.view_tradelist.bin_name"),field:"bin_name",align:"center"},{name:"goods_desc",label:this.$t("goods.view_goodslist.goods_desc"),field:"goods_desc",align:"center"},{name:"mode_code",required:!0,label:this.$t("dashboards.view_tradelist.mode_code"),align:"center",field:"mode_code"},{name:"goods_qty",label:this.$t("dashboards.view_tradelist.goods_qty"),field:"goods_qty",align:"center"},{name:"creater",label:this.$t("dashboards.view_tradelist.creater"),field:"creater",align:"center"},{name:"create_time",label:this.$t("dashboards.view_tradelist.create_time"),field:"create_time",align:"center"},{name:"update_time",label:this.$t("dashboards.view_tradelist.update_time"),field:"update_time",align:"right"}],pagination:{page:1,rowsPerPage:"30"},current:1,max:0,total:0,paginationIpt:1}},methods:{getList(){var t=this;Object(o["f"])("cyclecount/qtyrecorviewset/?page="+t.current,{}).then((e=>{t.table_list=e.results,t.total=e.count,0===e.count||1===Math.ceil(e.count/30)?t.max=0:t.max=Math.ceil(e.count/30),t.pathname_previous=e.previous,t.pathname_next=e.next})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},changePageEnter(t){Number(this.paginationIpt)<1?(this.current=1,this.paginationIpt=1):Number(this.paginationIpt)>this.max?(this.current=this.max,this.paginationIpt=this.max):this.current=Number(this.paginationIpt),this.getList()},getSearchList(){var t=this;t.current=1,t.paginationIpt=1,Object(o["f"])("cyclecount/qtyrecorviewset/?goods_code__icontains="+t.filter+"&page="+t.current,{}).then((e=>{t.table_list=e.results,t.total=e.count,0===e.count||1===Math.ceil(e.count/30)?t.max=0:t.max=Math.ceil(e.count/30),t.pathname_previous=e.previous,t.pathname_next=e.next})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},getListPrevious(){this.$q.localStorage.has("auth")&&Object(o["f"])(this.pathname_previous,{}).then((t=>{this.table_list=t.results,this.pathname_previous=t.previous,this.pathname_next=t.next})).catch((t=>{this.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},getListNext(){this.$q.localStorage.has("auth")&&Object(o["f"])(this.pathname_next,{}).then((t=>{this.table_list=t.results,this.pathname_previous=t.previous,this.pathname_next=t.next})).catch((t=>{this.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},reFresh(){this.table_list=[],this.getList()}},created(){var t=this;s["a"].has("openid")?t.openid=s["a"].getItem("openid"):(t.openid="",s["a"].set("openid","")),s["a"].has("login_name")?t.login_name=s["a"].getItem("login_name"):(t.login_name="",s["a"].set("login_name","")),s["a"].has("auth")?(t.authin="1",t.getList()):t.authin="0"},mounted(){this.$q.platform.is.electron?this.height=String(this.$q.screen.height-290)+"px":this.height=this.$q.screen.height-290+"px"}},l=r,c=a("42e1"),d=a("eaac"),p=a("e7a9"),h=a("9c40"),u=a("05c0"),g=a("2c91"),_=a("27f9"),m=a("0016"),b=a("bd08"),f=a("db86"),v=a("3b16"),x=a("eebe"),y=a.n(x),q=Object(c["a"])(l,n,i,!1,null,null,null);e["default"]=q.exports;y()(q,"components",{QTable:d["a"],QBtnGroup:p["a"],QBtn:h["a"],QTooltip:u["a"],QSpace:g["a"],QInput:_["a"],QIcon:m["a"],QTr:b["a"],QTd:f["a"],QPagination:v["a"]})}}]);