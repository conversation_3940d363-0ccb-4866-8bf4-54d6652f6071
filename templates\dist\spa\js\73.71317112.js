(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[73],{cde3:function(t,a,n){"use strict";n.r(a);var e=function(){var t=this,a=t._self._c;return a("q-page",{staticClass:"flex flex-top"},[[a("div",{staticClass:"q-pa-md"},[a("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[a("q-tabs",{model:{value:t.detaillink,callback:function(a){t.detaillink=a},expression:"detaillink"}},[a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"asn",label:t.$t("inbound.asn"),icon:"img:statics/inbound/asn.png",to:{name:"asn"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"predeliverystock",label:t.$t("inbound.predeliverystock"),icon:"img:statics/inbound/polist.png",to:{name:"predeliverystock"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"preloadstock",label:t.$t("inbound.preloadstock"),icon:"img:statics/inbound/preloadstock.png",to:{name:"preloadstock"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"presortstock",label:t.$t("inbound.presortstock"),icon:"img:statics/inbound/presortstock.png",to:{name:"presortstock"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"sortstock",label:t.$t("inbound.sortstock"),icon:"img:statics/inbound/sortstock.png",to:{name:"sortstock"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"shortage",label:t.$t("inbound.shortage"),icon:"img:statics/inbound/shortage.png",to:{name:"shortage"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"more",label:t.$t("inbound.more"),icon:"img:statics/inbound/more.png",to:{name:"more"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"asnfinish",label:t.$t("inbound.asnfinish"),icon:"img:statics/inbound/asnfinish.png",to:{name:"asnfinish"},exact:""}})],1)],1)],1)])],a("div",{staticClass:"main-table"},[a("router-view")],1)],2)},s=[],o={name:"Pageinbound",data(){return{detaillink:"asn"}},methods:{}},i=o,r=n("42e1"),c=n("9989"),l=n("429b"),m=n("7867"),d=n("eebe"),p=n.n(d),b=Object(r["a"])(i,e,s,!1,null,null,null);a["default"]=b.exports;p()(b,"components",{QPage:c["a"],QTabs:l["a"],QRouteTab:m["a"]})}}]);