(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[78],{"1be3":function(a,t,e){"use strict";e.r(t);var i=function(){var a=this,t=a._self._c;return t("q-page",{staticClass:"flex flex-top"},[[t("div",{staticClass:"q-pa-md"},[t("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[t("q-tabs",{model:{value:a.detaillink,callback:function(t){a.detaillink=t},expression:"detaillink"}},[t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"initializeupload",label:a.$t("upload_center.initializeupload"),icon:"img:statics/uploadcenter/uploadinbound.png",to:{name:"initializeupload"},exact:""}})],1),t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"addupload",label:a.$t("upload_center.addupload"),icon:"img:statics/uploadcenter/addupload.png",to:{name:"addupload"},exact:""}})],1)],1)],1)])],t("div",{staticClass:"main-table2"},[t("router-view")],1)],2)},n=[],l={name:"upload",data(){return{detaillink:"initializeupload"}}},o=l,d=e("42e1"),s=e("9989"),u=e("429b"),p=e("7867"),c=e("eebe"),r=e.n(c),m=Object(d["a"])(o,i,n,!1,null,null,null);t["default"]=m.exports;r()(m,"components",{QPage:s["a"],QTabs:u["a"],QRouteTab:p["a"]})}}]);