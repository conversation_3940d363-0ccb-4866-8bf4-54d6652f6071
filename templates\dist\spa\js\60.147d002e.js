(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[60],{6207:function(e,t,a){"use strict";var r=a("a13e"),o=a.n(r);t["default"]=o.a},a13e:function(e,t){},cce9:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",[t("transition",{attrs:{appear:"","enter-active-class":"animated fadeIn"}},[t("q-table",{staticClass:"my-sticky-header-column-table shadow-24",attrs:{data:e.table_list,"row-key":"id",separator:e.separator,loading:e.loading,filter:e.filter,columns:e.columns,"hide-bottom":"",pagination:e.pagination,"no-data-label":"No data","no-results-label":"No data you want","table-style":{height:e.height},flat:"",bordered:""},on:{"update:pagination":function(t){e.pagination=t}},scopedSlots:e._u([{key:"top",fn:function(){return[t("q-btn-group",{attrs:{push:""}},[t("q-btn",{attrs:{label:e.$t("new"),icon:"add"},on:{click:function(t){return e.newFormCheck()}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n             "+e._s(e.$t("newtip"))+"\n           ")])],1),t("q-btn",{attrs:{label:e.$t("refresh"),icon:"refresh"},on:{click:function(t){return e.reFresh()}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n             "+e._s(e.$t("refreshtip"))+"\n           ")])],1)],1),t("q-space")]},proxy:!0},{key:"body",fn:function(a){return[t("q-tr",{attrs:{props:a}},[a.row.id===e.editid?[t("q-td",{key:"warehouse_name",attrs:{props:a}},[t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("warehouse.view_warehouse.warehouse_name"),autofocus:"",rules:[t=>t&&t.length>0||e.error1]},model:{value:e.editFormData.warehouse_name,callback:function(t){e.$set(e.editFormData,"warehouse_name",t)},expression:"editFormData.warehouse_name"}})],1)]:a.row.id!==e.editid?[t("q-td",{key:"warehouse_name",attrs:{props:a}},[e._v("\n             "+e._s(a.row.warehouse_name)+"\n           ")])]:e._e(),a.row.id===e.editid?[t("q-td",{key:"warehouse_city",attrs:{props:a}},[t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("warehouse.view_warehouse.warehouse_city"),rules:[t=>t&&t.length>0||e.error2]},model:{value:e.editFormData.warehouse_city,callback:function(t){e.$set(e.editFormData,"warehouse_city",t)},expression:"editFormData.warehouse_city"}})],1)]:a.row.id!==e.editid?[t("q-td",{key:"warehouse_city",attrs:{props:a}},[e._v("\n             "+e._s(a.row.warehouse_city)+"\n           ")])]:e._e(),a.row.id===e.editid?[t("q-td",{key:"warehouse_address",attrs:{props:a}},[t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("warehouse.view_warehouse.warehouse_address"),rules:[t=>t&&t.length>0||e.error3]},model:{value:e.editFormData.warehouse_address,callback:function(t){e.$set(e.editFormData,"warehouse_address",t)},expression:"editFormData.warehouse_address"}})],1)]:a.row.id!==e.editid?[t("q-td",{key:"warehouse_address",attrs:{props:a}},[e._v("\n             "+e._s(a.row.warehouse_address)+"\n           ")])]:a.row.id!==e.editid?[t("q-td",{key:"warehouse_contact",attrs:{props:a}},[e._v("\n             "+e._s(a.row.warehouse_contact)+"\n           ")])]:e._e(),a.row.id===e.editid?[t("q-td",{key:"warehouse_contact",attrs:{props:a}},[t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("warehouse.view_warehouse.warehouse_contact"),rules:[t=>t&&t.length>0||e.error4]},model:{value:e.editFormData.warehouse_contact,callback:function(t){e.$set(e.editFormData,"warehouse_contact",t)},expression:"editFormData.warehouse_contact"}})],1)]:a.row.id!==e.editid?[t("q-td",{key:"warehouse_contact",attrs:{props:a}},[e._v("\n             "+e._s(a.row.warehouse_contact)+"\n           ")])]:e._e(),a.row.id===e.editid?[t("q-td",{key:"warehouse_manager",attrs:{props:a}},[t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("warehouse.view_warehouse.warehouse_manager"),rules:[t=>t&&t.length>0||e.error5]},model:{value:e.editFormData.warehouse_manager,callback:function(t){e.$set(e.editFormData,"warehouse_manager",t)},expression:"editFormData.warehouse_manager"}})],1)]:a.row.id!==e.editid?[t("q-td",{key:"warehouse_manager",attrs:{props:a}},[e._v("\n             "+e._s(a.row.warehouse_manager)+"\n           ")])]:e._e(),t("q-td",{key:"creater",attrs:{props:a}},[e._v("\n           "+e._s(a.row.creater)+"\n         ")]),t("q-td",{key:"create_time",attrs:{props:a}},[e._v("\n           "+e._s(a.row.create_time)+"\n         ")]),t("q-td",{key:"update_time",attrs:{props:a}},[e._v("\n           "+e._s(a.row.update_time)+"\n         ")]),e.editMode?e.editMode?[a.row.id===e.editid?[t("q-td",{key:"action",staticStyle:{width:"100px"},attrs:{props:a}},[t("q-btn",{attrs:{round:"",flat:"",push:"",color:"secondary",icon:"check"},on:{click:function(t){return e.editDataSubmit()}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n                "+e._s(e.$t("confirmedit"))+"\n              ")])],1),t("q-btn",{attrs:{round:"",flat:"",push:"",color:"red",icon:"close"},on:{click:function(t){return e.editDataCancel()}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n                "+e._s(e.$t("canceledit"))+"\n              ")])],1)],1)]:a.row.id!==e.editid?void 0:e._e()]:e._e():[t("q-td",{key:"action",staticStyle:{width:"100px"},attrs:{props:a}},[t("q-btn",{attrs:{round:"",flat:"",push:"",color:"purple",icon:"edit"},on:{click:function(t){return e.editData(a.row)}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n                "+e._s(e.$t("edit"))+"\n              ")])],1),t("q-btn",{attrs:{round:"",flat:"",push:"",color:"dark",icon:"delete"},on:{click:function(t){return e.deleteData(a.row.id)}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n                "+e._s(e.$t("delete"))+"\n              ")])],1),t("q-btn",{attrs:{round:"",flat:"",push:"",color:"green",icon:"publish"},on:{click:function(t){return e.publishData(a.row)}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n                "+e._s(e.$t("warehouse.view_warehouse.publish_warehouse"))+"\n              ")])],1),t("q-btn",{attrs:{round:"",flat:"",push:"",color:"red",icon:"backspace"},on:{click:function(t){return e.NopublishData(a.row)}}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4",offset:[10,10],"content-style":"font-size: 12px"}},[e._v("\n                "+e._s(e.$t("warehouse.view_warehouse.Nopublish_warehouse"))+"\n              ")])],1)],1)]],2)]}}])})],1),[t("div",{directives:[{name:"show",rawName:"v-show",value:0!==e.max,expression:"max !== 0"}],staticClass:"q-pa-lg flex flex-center"},[t("div",[e._v(e._s(e.total)+" ")]),t("q-pagination",{attrs:{color:"black",max:e.max,"max-pages":6,"boundary-links":""},on:{click:function(t){return e.getList()}},model:{value:e.current,callback:function(t){e.current=t},expression:"current"}}),t("div",[t("input",{directives:[{name:"model",rawName:"v-model",value:e.paginationIpt,expression:"paginationIpt"}],staticStyle:{width:"60px","text-align":"center"},domProps:{value:e.paginationIpt},on:{blur:e.changePageEnter,keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.changePageEnter.apply(null,arguments)},input:function(t){t.target.composing||(e.paginationIpt=t.target.value)}}})])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:0===e.max,expression:"max === 0"}],staticClass:"q-pa-lg flex flex-center"},[t("q-btn",{attrs:{flat:"",push:"",color:"dark",label:e.$t("no_data")}})],1)],t("q-dialog",{model:{value:e.newForm,callback:function(t){e.newForm=t},expression:"newForm"}},[t("q-card",{staticClass:"shadow-24"},[t("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[t("div",[e._v(e._s(e.$t("newtip")))]),t("q-space"),t("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[e._v(e._s(e.$t("index.close")))])],1)],1),t("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("warehouse.view_warehouse.warehouse_name"),autofocus:"",rules:[t=>t&&t.length>0||e.error1]},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.newDataSubmit()}},model:{value:e.newFormData.warehouse_name,callback:function(t){e.$set(e.newFormData,"warehouse_name",t)},expression:"newFormData.warehouse_name"}}),t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("warehouse.view_warehouse.warehouse_city"),rules:[t=>t&&t.length>0||e.error2]},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.newDataSubmit()}},model:{value:e.newFormData.warehouse_city,callback:function(t){e.$set(e.newFormData,"warehouse_city",t)},expression:"newFormData.warehouse_city"}}),t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("warehouse.view_warehouse.warehouse_address"),rules:[t=>t&&t.length>0||e.error3]},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.newDataSubmit()}},model:{value:e.newFormData.warehouse_address,callback:function(t){e.$set(e.newFormData,"warehouse_address",t)},expression:"newFormData.warehouse_address"}}),t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("warehouse.view_warehouse.warehouse_contact"),rules:[t=>t&&t.length>0||e.error4]},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.newDataSubmit()}},model:{value:e.newFormData.warehouse_contact,callback:function(t){e.$set(e.newFormData,"warehouse_contact",t)},expression:"newFormData.warehouse_contact"}}),t("q-input",{attrs:{dense:"",outlined:"",square:"",label:e.$t("warehouse.view_warehouse.warehouse_manager"),rules:[t=>t&&t.length>0||e.error5]},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.newDataSubmit()}},model:{value:e.newFormData.warehouse_manager,callback:function(t){e.$set(e.newFormData,"warehouse_manager",t)},expression:"newFormData.warehouse_manager"}})],1),t("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[t("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(t){return e.newDataCancel()}}},[e._v(e._s(e.$t("cancel")))]),t("q-btn",{attrs:{color:"primary"},on:{click:function(t){return e.newDataSubmit()}}},[e._v(e._s(e.$t("submit")))])],1)],1)],1),t("q-dialog",{model:{value:e.deleteForm,callback:function(t){e.deleteForm=t},expression:"deleteForm"}},[t("q-card",{staticClass:"shadow-24"},[t("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[t("div",[e._v(e._s(e.$t("delete")))]),t("q-space"),t("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[e._v(e._s(e.$t("index.close")))])],1)],1),t("q-card-section",{staticClass:"scroll",staticStyle:{"max-height":"325px",width:"400px"}},[e._v("\n       "+e._s(e.$t("deletetip"))+"\n     ")]),t("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[t("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(t){return e.deleteDataCancel()}}},[e._v(e._s(e.$t("cancel")))]),t("q-btn",{attrs:{color:"primary"},on:{click:function(t){return e.deleteDataSubmit()}}},[e._v(e._s(e.$t("submit")))])],1)],1)],1),t("q-dialog",{model:{value:e.publishForm,callback:function(t){e.publishForm=t},expression:"publishForm"}},[t("q-card",{staticClass:"shadow-24"},[t("q-bar",{staticClass:"bg-light-blue-10 text-white rounded-borders",staticStyle:{height:"50px"}},[t("div",[e._v(e._s(e.$t("warehouse.view_warehouse.square_measure")))]),t("q-space"),t("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{dense:"",flat:"",icon:"close"}},[t("q-tooltip",{attrs:{"content-class":"bg-amber text-black shadow-4"}},[e._v(e._s(e.$t("index.close")))])],1)],1),t("q-card-section",{staticClass:"q-pt-none"},[t("q-input",{attrs:{dense:"",autofocus:""},model:{value:e.square_measure,callback:function(t){e.square_measure=t},expression:"square_measure"}})],1),t("div",{staticStyle:{float:"right",padding:"15px 15px 15px 0"}},[t("q-btn",{staticStyle:{"margin-right":"25px"},attrs:{color:"white","text-color":"black"},on:{click:function(t){return e.publishCancel()}}},[e._v(e._s(e.$t("cancel")))]),t("q-btn",{attrs:{color:"primary"},on:{click:function(t){return e.publishDataSubmit()}}},[e._v(e._s(e.$t("submit")))])],1)],1)],1)],2)},o=[],s=a("3004"),n=a("18d6"),i=a("bc3a"),l=a.n(i),c={name:"Pagewarehouse",data(){return{openid:"",login_name:"",authin:"0",pathname:"warehouse/",pathname_previous:"",pathname_next:"",separator:"cell",loading:!1,height:"",table_list:[],columns:[{name:"warehouse_name",required:!0,label:this.$t("warehouse.view_warehouse.warehouse_name"),align:"left",field:"warehouse_name"},{name:"warehouse_city",label:this.$t("warehouse.view_warehouse.warehouse_city"),field:"warehouse_city",align:"center"},{name:"warehouse_address",label:this.$t("warehouse.view_warehouse.warehouse_address"),field:"warehouse_address",align:"center"},{name:"warehouse_contact",label:this.$t("warehouse.view_warehouse.warehouse_contact"),field:"warehouse_contact",align:"center"},{name:"warehouse_manager",label:this.$t("warehouse.view_warehouse.warehouse_manager"),field:"warehouse_manager",align:"center"},{name:"creater",label:this.$t("creater"),field:"creater",align:"center"},{name:"create_time",label:this.$t("createtime"),field:"create_time",align:"center"},{name:"update_time",label:this.$t("updatetime"),field:"update_time",align:"center"},{name:"action",label:this.$t("action"),align:"right"}],filter:"",pagination:{page:1,rowsPerPage:"30"},newForm:!1,newFormData:{warehouse_name:"",warehouse_city:"",warehouse_address:"",warehouse_contact:"",warehouse_manager:"",creater:""},editid:0,editFormData:{},editMode:!1,deleteForm:!1,deleteid:0,publishForm:!1,publishdetail:"",square_measure:"",error1:this.$t("warehouse.view_warehouseset.error1"),error2:this.$t("warehouse.view_warehouseset.error2"),error3:this.$t("warehouse.view_warehouseset.error3"),error4:this.$t("warehouse.view_warehouseset.error4"),error5:this.$t("warehouse.view_warehouseset.error5"),current:1,max:0,total:0,paginationIpt:1}},methods:{getList(){var e=this;n["a"].has("auth")&&Object(s["f"])(e.pathname+"?page="+e.current,{}).then((t=>{e.table_list=t.results,e.total=t.count,0===t.count||1===Math.ceil(t.count/30)?e.max=0:e.max=Math.ceil(t.count/30),e.pathname_previous=t.previous,e.pathname_next=t.next})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},changePageEnter(e){Number(this.paginationIpt)<1?(this.current=1,this.paginationIpt=1):Number(this.paginationIpt)>this.max?(this.current=this.max,this.paginationIpt=this.max):this.current=Number(this.paginationIpt),this.getList()},getListPrevious(){var e=this;n["a"].has("auth")&&Object(s["f"])(e.pathname_previous,{}).then((t=>{e.table_list=t.results,e.pathname_previous=t.previous,e.pathname_next=t.next})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},getListNext(){var e=this;n["a"].has("auth")&&Object(s["f"])(e.pathname_next,{}).then((t=>{e.table_list=t.results,e.pathname_previous=t.previous,e.pathname_next=t.next})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},reFresh(){var e=this;e.getList()},newFormCheck(){var e=this;e.table_list.length>=1?e.$q.notify({message:"You Just Can Create 1 Line Data",icon:"close",color:"negative"}):e.newForm=!0},newDataSubmit(){var e=this,t=[];e.table_list.forEach((e=>{t.push(e.warehouse_name)})),-1===t.indexOf(e.newFormData.warehouse_name)&&0!==e.newFormData.warehouse_name.length?(e.newFormData.creater=e.login_name,Object(s["i"])(e.pathname,e.newFormData).then((t=>{e.getList(),e.newDataCancel(),e.$q.notify({message:"Success Create",icon:"check",color:"green"})})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))):(e.newFormData.creater=e.login_name,Object(s["i"])(e.pathname,e.newFormData).then((t=>{e.getList(),e.newDataCancel(),e.$q.notify({message:"",icon:"close",color:"negative"})})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))),t=[]},newDataCancel(){var e=this;e.newForm=!1,e.newFormData={warehouse_name:"",warehouse_city:"",warehouse_address:"",warehouse_contact:"",warehouse_manager:"",creater:""}},editData(e){var t=this;t.editMode=!0,t.editid=e.id,t.editFormData={warehouse_name:e.warehouse_name,warehouse_city:e.warehouse_city,warehouse_address:e.warehouse_address,warehouse_contact:e.warehouse_contact,warehouse_manager:e.warehouse_manager,creater:t.login_name}},editDataSubmit(){var e=this;Object(s["j"])(e.pathname+e.editid+"/",e.editFormData).then((t=>{e.editDataCancel(),e.getList(),e.$q.notify({message:"Success Edit Data",icon:"check",color:"green"})})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},editDataCancel(){var e=this;e.editMode=!1,e.editid=0,e.editFormData={warehouse_name:"",warehouse_city:"",warehouse_address:"",warehouse_contact:"",warehouse_manager:"",creater:""}},deleteData(e){var t=this;t.deleteForm=!0,t.deleteid=e},deleteDataSubmit(){var e=this;Object(s["d"])(e.pathname+e.deleteid+"/").then((t=>{e.deleteDataCancel(),e.getList(),e.$q.notify({message:"Success Edit Data",icon:"check",color:"green"})})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},deleteDataCancel(){var e=this;e.deleteForm=!1,e.deleteid=0},publishData(e){var t=this;t.publishForm=!0,t.publishdetail=e},publishDataSubmit(){var e=this;e.publishdetail.openid=n["a"].getItem("openid"),e.publishdetail.square_measure=e.square_measure,l.a.post("https://po.56yhz.com/warehouse/",e.publishdetail).then((t=>{e.publishCancel(),e.getList(),e.$q.notify({message:"Success Publish Data",icon:"check",color:"green"})})).catch((t=>{e.$q.notify({message:t.detail,icon:"close",color:"negative"})}))},NopublishData(e){var t=this;t.publishdetail=e,t.publishdetail.openid=n["a"].getItem("openid"),l.a.put("https://po.56yhz.com/warehouse/",t.publishdetail).then((e=>{t.publishCancel(),t.getList(),t.$q.notify({message:"Success Backspace Data",icon:"check",color:"green"})})).catch((e=>{t.$q.notify({message:e.detail,icon:"close",color:"negative"})}))},publishCancel(){var e=this;e.publishForm=!1,e.publishdetail=""}},created(){var e=this;n["a"].has("openid")?e.openid=n["a"].getItem("openid"):(e.openid="",n["a"].set("openid","")),n["a"].has("login_name")?e.login_name=n["a"].getItem("login_name"):(e.login_name="",n["a"].set("login_name","")),n["a"].has("auth")?(e.authin="1",e.getList()):e.authin="0"},mounted(){var e=this;e.$q.platform.is.electron?e.height=String(e.$q.screen.height-290)+"px":e.height=e.$q.screen.height-290+"px"},updated(){},destroyed(){}},u=c,d=a("42e1"),h=a("6207"),p=a("eaac"),w=a("e7a9"),m=a("9c40"),_=a("05c0"),b=a("2c91"),g=a("bd08"),f=a("db86"),v=a("27f9"),y=a("3b16"),q=a("24e8"),k=a("f09f"),x=a("d847"),D=a("a370"),$=a("7f67"),F=a("eebe"),C=a.n(F),S=Object(d["a"])(u,r,o,!1,null,null,null);"function"===typeof h["default"]&&Object(h["default"])(S);t["default"]=S.exports;C()(S,"components",{QTable:p["a"],QBtnGroup:w["a"],QBtn:m["a"],QTooltip:_["a"],QSpace:b["a"],QTr:g["a"],QTd:f["a"],QInput:v["a"],QPagination:y["a"],QDialog:q["a"],QCard:k["a"],QBar:x["a"],QCardSection:D["a"]}),C()(S,"directives",{ClosePopup:$["a"]})}}]);