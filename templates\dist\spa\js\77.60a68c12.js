(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[77],{"71a3":function(a,t,s){"use strict";s.r(t);var n=function(){var a=this,t=a._self._c;return t("q-page",{staticClass:"flex flex-top"},[[t("div",{staticClass:"q-pa-md"},[t("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[t("q-tabs",{model:{value:a.detaillink,callback:function(t){a.detaillink=t},expression:"detaillink"}},[t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"supplierasnlist",label:a.$t("inbound.asn"),icon:"img:statics/inbound/asn.png",to:{name:"supplierasnlist"},to:"/supplierasn/supplierasnlist",exact:""}})],1),t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"supplierasnfinish",label:a.$t("inbound.asnfinish"),icon:"img:statics/inbound/asnfinish.png",to:{name:"supplierasnfinish"},exact:""}})],1)],1)],1)])],t("div",{staticClass:"main-table"},[t("router-view")],1)],2)},i=[],e={name:"Pagesupplierasn",data(){return{detaillink:"supplierasnlist"}},methods:{}},l=e,o=s("42e1"),p=s("9989"),r=s("429b"),u=s("7867"),c=s("eebe"),d=s.n(c),b=Object(o["a"])(l,n,i,!1,null,null,null);t["default"]=b.exports;d()(b,"components",{QPage:p["a"],QTabs:r["a"],QRouteTab:u["a"]})}}]);