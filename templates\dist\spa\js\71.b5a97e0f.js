(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[71],{f7c9:function(a,t,e){"use strict";e.r(t);var i=function(){var a=this,t=a._self._c;return t("q-page",{staticClass:"flex flex-top"},[[t("div",{staticClass:"q-pa-md"},[t("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[t("q-tabs",{model:{value:a.detaillink,callback:function(t){a.detaillink=t},expression:"detaillink"}},[t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"capitallist",label:a.$t("finance.capital"),icon:"img:statics/capital/capital.png",to:{name:"capitallist"},exact:""}})],1),t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"freight",label:a.$t("finance.freight"),icon:"img:statics/capital/freight.png",to:{name:"freight"},exact:""}})],1)],1)],1)])],t("div",{staticClass:"main-table"},[t("router-view")],1)],2)},n=[],l={name:"Pagecapital",data(){return{detaillink:"capitallist"}},methods:{}},s=l,c=e("42e1"),r=e("9989"),o=e("429b"),p=e("7867"),d=e("eebe"),m=e.n(d),u=Object(c["a"])(s,i,n,!1,null,null,null);t["default"]=u.exports;m()(u,"components",{QPage:r["a"],QTabs:o["a"],QRouteTab:p["a"]})}}]);