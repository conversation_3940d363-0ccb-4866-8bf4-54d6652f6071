(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[75],{c8d8:function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t._self._c;return a("q-page",{staticClass:"flex flex-top"},[[a("div",{staticClass:"q-pa-md"},[a("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[a("q-tabs",{model:{value:t.detaillink,callback:function(a){t.detaillink=a},expression:"detaillink"}},[a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"stafflist",label:t.$t("staff.staff"),icon:"perm_contact_calendar",to:{name:"stafflist"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"stafflist_check_code",label:t.$t("staff.check_code"),icon:"published_with_changes",to:{name:"stafflist_check_code"},exact:""}})],1),a("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[a("q-route-tab",{attrs:{name:"stafftype",label:t.$t("staff.view_staff.staff_type"),icon:"img:statics/staff/stafftype.png",to:{name:"stafftype"},exact:""}})],1)],1)],1)])],a("div",{style:{width:"100%",margin:"-10px 10px 0 10px"}},[a("router-view")],1)],2)},n=[],i={name:"Pagestaff",data(){return{detaillink:"stafflist"}},methods:{}},l=i,c=e("42e1"),f=e("9989"),o=e("429b"),r=e("7867"),p=e("eebe"),d=e.n(p),m=Object(c["a"])(l,s,n,!1,null,null,null);a["default"]=m.exports;d()(m,"components",{QPage:f["a"],QTabs:o["a"],QRouteTab:r["a"]})}}]);