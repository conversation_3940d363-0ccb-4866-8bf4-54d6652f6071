(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[79],{"4d75":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("q-page",{staticClass:"flex flex-top"},[[t("div",{staticClass:"q-pa-md"},[t("div",{staticClass:"q-gutter-y-md",staticStyle:{"max-width":"100%"}},[t("q-tabs",{model:{value:e.detaillink,callback:function(t){e.detaillink=t},expression:"detaillink"}},[t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"warehouseset",label:e.$t("warehouse.warehouse"),icon:"img:statics/warehouse/warehouseset.png",to:{name:"warehouseset"},exact:""}})],1),t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"binset",label:e.$t("warehouse.binset"),icon:"img:statics/warehouse/binset.png",to:{name:"binset"},exact:""}})],1),t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"binsize",label:e.$t("warehouse.binsize"),icon:"img:statics/warehouse/binsize.png",to:{name:"binsize"},exact:""}})],1),t("transition",{attrs:{appear:"","enter-active-class":"animated zoomIn"}},[t("q-route-tab",{attrs:{name:"property",label:e.$t("warehouse.property"),icon:"img:statics/warehouse/property.png",to:{name:"property"},exact:""}})],1)],1)],1)])],t("div",{staticClass:"main-table"},[t("router-view")],1)],2)},n=[],i={name:"Pagewarehouse",data(){return{detaillink:"warehouseset"}},methods:{}},r=i,o=a("42e1"),l=a("9989"),c=a("429b"),u=a("7867"),p=a("eebe"),m=a.n(p),b=Object(o["a"])(r,s,n,!1,null,null,null);t["default"]=b.exports;m()(b,"components",{QPage:l["a"],QTabs:c["a"],QRouteTab:u["a"]})}}]);